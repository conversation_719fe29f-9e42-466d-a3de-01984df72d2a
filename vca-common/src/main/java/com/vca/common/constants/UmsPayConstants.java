package com.vca.common.constants;


/**
 * @Description: 银联商户 全民付 常量类
 * @Author: Li
 * @Date: 2022/10/27 10:43
 *
 */
public class UmsPayConstants {

    public static final String UMS_PAY_APPID = "pay_ums_appid";

    public static final String UMS_PAY_APPKEY = "pay_ums_appkey";

    public static final String UMS_PAY_MID = "pay_ums_mid";

    public static final String UMS_PAY_TID = "pay_ums_tid";

    public static final String UMS_SOURCE_NO="pay_ums_sourceno";

    //微信
    public static final String UMS_PAY_WX= "wxpay";

    //支付宝
    public static final String UMS_PAY_ALIPAY= "alipay";

    //云闪付
    public static final String UMS_PAY_UNIONPAY= "unionpay";

    //新订单
    public static final String UMS_PAY_STATUS_NEW_ORDER="NEW_ORDER";

    //不明确的交易状态
    public static final String UMS_PAY_STATUS_UNKNOWN="UNKNOWN";

    //在指定时间段内未支付时关闭的交易；在交易完成全额退款成功时关闭的交易；支付失败的交易。
    //超时未支付 或 交易失败
    public static final String UMS_PAY_STATUS_TRADE_CLOSED ="TRADE_CLOSED";

    //交易创建，等待买家付款。
    public static final String UMS_PAY_STATUS_WAIT_BUYER_PAY="WAIT_BUYER_PAY";

    //交易成功 支付完成
    public static final String UMS_PAY_STATUS_TRADE_SUCCESS="TRADE_SUCCESS";

    //订单转入退货流程
    public static final String UMS_PAY_STATUS_TRADE_REFUND ="TRADE_REFUND";

    //退款处理中
    public static final String UMS_PAY_REFUND_STATUS_PROCESSING = "PROCESSING";

    //退款成功
    public static final String UMS_PAY_REFUND_STATUS_SUCCESS = "SUCCESS";

    //退款失败
    public static final String UMS_PAY_REFUND_STATUS_FAIL = "FAIL";

    //退款状态未知
    public static final String UMS_PAY_REFUND_STATUS_UNKNOWN = "UNKNOWN";

    //银联 全民付 平台错误码 成功
    public static final String UMS_PAY_ERRCODE_STATUS_SUCCESS = "SUCCESS";

    //小程序支付
    public static final String UMS_PAY_INSTMID_MINIDEFAULT="MINIDEFAULT";

    //退款
    public static final String UMS_PAY_INSTMID_YUEDANDEFAULT="YUEDANDEFAULT";

    //查询订单
    public static final String UMS_PAY_INSTMID_QRPAYDEFAULT="QRPAYDEFAULT";

    // 交易方式 小程序
    public static final String UMS_PAY_TRADETYPE_MINI="MINI";

    //支付回调地址
    public static final String UMS_PAY_NOTIFY_API_URI = "/api/admin/open/ums/resultInform";

    public static final String UMS_PAY_HEADER="Authorization";


}
