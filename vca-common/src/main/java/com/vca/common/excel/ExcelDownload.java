package com.vca.common.excel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName ExcelDownload
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2025/1/2 17:26
 **/
@Data
@TableName("vca_excel_download")
@EqualsAndHashCode(callSuper = false)
public class ExcelDownload implements Serializable {

    private static final long serialVersionUID = 7256553336515444794L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String title;
    private String timePeriod;
    private String type;
    private String downloadUrl;
    private Date createTime;
    private String status;




}
