package com.vca.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.vca.common.model.course.Course;
import com.vca.common.model.course.CourseAbout;
import com.vca.common.request.AdminCommonRequest;
import com.vca.common.request.CourseAddRequest;
import com.vca.common.request.CourseFilterRequest;
import com.vca.common.request.PageParamRequest;
import com.vca.common.request.SelectCourseRequest;
import com.vca.common.response.AdminHeaderResponse;
import com.vca.common.response.CourseAboutResponse;
import com.vca.common.response.CourseAdminListResponse;
import com.vca.common.response.CourseAdminResponse;
import com.vca.common.response.SelectCourseResponse;
import com.vca.common.vo.CourseListAllVo;
import com.vca.common.vo.CourseListVo;
import com.vca.common.vo.CourseVo;
import com.vca.common.vo.MyRecord;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 课程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
public interface CourseService extends IService<Course> {

    /**
     * @Description:课程详情列表
     * @Author: chenBing
     * @Date: 2022/9/23
     */
    CourseVo info(String language,Long courseId);

    String getAccessToken();

    List<CourseAboutResponse> getCourseAbouts(String language,Integer type, Integer typeMainCourse, Integer mainId);

    /**
     * @Description:获取相关课程或可能喜欢的商品
     * @Author: chenBing
     * @Date: 2022/11/3
     */
    List<CourseAbout> getCourseAbout(Integer type, Integer typeMainCourse, Integer selectId);

    /**
     * @Description:处理相关课程信息
     * @Author: chenBing
     * @Date: 2022/11/3
     */
    CourseAboutResponse processRelevantCourseInformation(String language,CourseAbout courseAbout);

    /**
     * @Description:新增课程
     * @Author: chenBing
     * @Date: 2022/11/7
     */
    Boolean addCourse(CourseAddRequest request);

    BigDecimal getPrice(Integer priceId);

    List<CourseVo> getListCalendar(String language,CourseFilterRequest request);

    /**
     * @Description:套课详情
     * @Author: chenBing
     * @Date: 2022/11/21
     */
    CourseVo packageInfo(String language,Long coursePackageId);

    /**
     * 课程管理
     *
     * <AUTHOR>
     * @date 2022/11/11 10:00
     */
    PageInfo<CourseAdminListResponse> getAdminList(AdminCommonRequest request, PageParamRequest pageParamRequest);

    /**
     * 课程排序清零
     *
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    boolean getSortClearZero();

    /**
     * 修改排序
     *
     * @param courseId 课程id
     * @param sort     排序
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    boolean updateSort(Integer courseId, Integer sort);

    /**
     * 修改排序
     *
     * @param courseId  课程id
     * @param seatCount 席位数量
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    boolean updateSeatCount(Integer courseId, Integer seatCount);

    /**
     * 上架课程
     *
     * @param courseId 课程id
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    boolean courseOnShell(Integer courseId);

    /**
     * 下架课程
     *
     * @param courseId 课程id
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    boolean courseOffShell(Integer courseId);

    /**
     * 根据id获取课程信息
     *
     * @param courseId
     * @return
     * <AUTHOR>
     * @date 2022/11/22 10:25
     */
    CourseAdminResponse getCourse(Long courseId);

    /**
     * @Description:修改课程
     * @Author: Li
     * @Date: 2022/11/22 14:56
     */
    boolean updateCourse(CourseAddRequest request);

    /**
     * 获得课程头部数据
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/23 09:16
     */
    List<AdminHeaderResponse> getHeader();

    /**
     * 删除课程
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/23 09:16
     */
    boolean deleteCourse(Long id);

    /**
     * @Description:列表
     * @Author: chenBing
     * @Date: 2022/11/25
     */
    List<CourseListVo> getListNoPage(String language,CourseFilterRequest request);


    List<Course> getCourseAndDelete(List<Long> ids);

    List<Integer> getCalendar(Integer month,Integer year);

    List<CourseVo> listNoScheduling(String language,CourseFilterRequest request);

    Map<String,Object> generateImage(Long courseId);
    
    /**
     * 获取课程选择列表
     * @param request 请求参数
     * @return 课程选择列表
     * <AUTHOR>
     * @date 2023-06-25
     */
    List<SelectCourseResponse> getSelectCourseList(SelectCourseRequest request);
}
