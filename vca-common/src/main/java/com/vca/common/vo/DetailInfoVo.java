package com.vca.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 票易通商品详情实体类
 * @date 2023-02-07 14:47
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "WxRefundVo", description = "票易通实体类")
public class DetailInfoVo implements Serializable {

    private static final long serialVersionUID = 5356662117172332563L;

    @ApiModelProperty(value = "商品明细ID 必须保证同一订单下该属性值唯一")
    private String orderItemNo;

    @ApiModelProperty(value = "明细名称")
    private String itemName;

    @ApiModelProperty(value = "规格")
    private String itemSpec;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "数量单位")
    private String quantityUnit;

    @ApiModelProperty(value = "单价")
    private String unitPrice;

    @ApiModelProperty(value = "已分摊折扣")
    private String outerDiscountWithTax;

    @ApiModelProperty(value = "商品编码")
    private String volunCode;

    @ApiModelProperty(value = "含税金额 数量*单价")
    private BigDecimal amountWithTax;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "商品税编")
    private String itemCode;

    @ApiModelProperty(value = "开票标示 默认0-开票，1-不开票")
    private String legalInvoiceFlag;

}
