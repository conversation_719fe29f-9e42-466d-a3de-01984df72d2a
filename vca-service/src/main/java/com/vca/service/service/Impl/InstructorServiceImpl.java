package com.vca.service.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.vca.common.excel.ExcelStyleHandler;
import com.vca.common.model.instructor.Instructor;
import com.vca.common.model.instructor.InstructorType;
import com.vca.common.page.CommonPage;
import com.vca.common.request.InstructorListRequest;
import com.vca.common.request.InstructorRequest;
import com.vca.common.request.PageParamRequest;
import com.vca.common.response.CourseResponse;
import com.vca.common.response.InstructorExportVO;
import com.vca.common.response.InstructorResponse;
import com.vca.common.response.InstructorTypeResponse;
import com.vca.common.response.UnavailableTimeResponse;
import com.vca.service.dao.instructor.InstructorDao;
import com.vca.service.service.InstructorCourseRelationService;
import com.vca.service.service.InstructorService;
import com.vca.service.service.InstructorTypeRelationService;
import com.vca.service.service.InstructorUnavailableTimeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 讲师服务实现类
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class InstructorServiceImpl extends ServiceImpl<InstructorDao, Instructor> implements InstructorService {

    private final InstructorTypeRelationService instructorTypeRelationService;
    private final InstructorUnavailableTimeService instructorUnavailableTimeService;
    private final InstructorCourseRelationService instructorCourseRelationService;
    private final InstructorDao instructorDao;

    /**
     * 讲师列表查询
     * @param param 查询参数，包含分页信息和查询条件
     * @return 封装了讲师列表的分页对象 {@link CommonPage<InstructorResponse>}
     */
    @Override
    public CommonPage<InstructorResponse> list(InstructorListRequest param) {
        // 1. 启动分页
        PageHelper.startPage(param.getPage(), param.getLimit());
        // 2. 执行主查询，获取分页后的讲师主信息
        Page<InstructorResponse> page = instructorDao.findInstructorPage(param);
        // 如果查询结果为空，直接返回一个空的分页对象
        if (page.isEmpty()) {
            return CommonPage.restPage(page);
        }
        // 3. 提取当前页所有讲师的ID
        List<Long> instructorIds = page.getResult().stream()
                .map(InstructorResponse::getId)
                .collect(Collectors.toList());

        // 4. 一次性批量查询所有关联数据
        List<InstructorTypeResponse> allTypes = instructorDao.findTypesByInstructorIds(instructorIds);
        List<CourseResponse> allCourses = instructorDao.findCoursesByInstructorIds(instructorIds);
        List<UnavailableTimeResponse> allUnavailableTimes = instructorDao.findUnavailableTimesByInstructorIds(instructorIds);


        // 5. 将关联数据转换为Map，方便高效查找
        Map<Long, List<InstructorTypeResponse>> typeMap = allTypes.stream()
                .collect(Collectors.groupingBy(InstructorTypeResponse::getInstructorId));

        Map<Long, List<CourseResponse>> courseMap = allCourses.stream()
                .collect(Collectors.groupingBy(CourseResponse::getInstructorId));

        Map<Long, List<UnavailableTimeResponse>> unavailableTimeMap = allUnavailableTimes.stream()
                .collect(Collectors.groupingBy(UnavailableTimeResponse::getInstructorId));


        // 6. 遍历分页结果，组装数据
        page.getResult().forEach(instructor -> {
            instructor.setTypes(typeMap.getOrDefault(instructor.getId(), Collections.emptyList()));
            instructor.setCourses(courseMap.getOrDefault(instructor.getId(), Collections.emptyList()));
            instructor.setUnavailableTimes(unavailableTimeMap.getOrDefault(instructor.getId(), Collections.emptyList()));
        });

        return CommonPage.restPage(page);
    }

    /**
     * 添加讲师信息，并保存关联数据
     * @param request 讲师请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInstructor(InstructorRequest request) {
        // 检查邮箱是否已存在
        checkEmailExists(request.getEmail(), null);

        // 检查手机号是否已存在
        checkPhoneExists(request.getPhoneCode(), request.getPhoneNumber(), null);

        Instructor instructor = new Instructor();
        BeanUtils.copyProperties(request, instructor);

        // 保存讲师
        this.save(instructor);

        // 保存讲师类型关系
        if (CollUtil.isNotEmpty(request.getTypeIds())) {
            instructorTypeRelationService.saveInstructorTypeRelations(instructor.getId(), request.getTypeIds());
        }

        // 保存不可授课时间
        if (CollUtil.isNotEmpty(request.getUnavailableTimes())) {
            instructorUnavailableTimeService.saveUnavailableTimes(instructor.getId(), request.getUnavailableTimes());
        }

        // 保存讲师课程关系
        if (CollUtil.isNotEmpty(request.getCourseIds())) {
            instructorCourseRelationService.saveInstructorCourseRelations(instructor.getId(), request.getCourseIds());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editInstructor(InstructorRequest request) {
        if (request.getId() == null) {
            throw new IllegalArgumentException("讲师ID不能为空");
        }

        // 检查讲师是否存在
        Instructor existInstructor = getById(request.getId());
        if (existInstructor == null || existInstructor.getIsDeleted()) {
            throw new IllegalArgumentException("讲师不存在");
        }

        // 检查邮箱是否已存在
        checkEmailExists(request.getEmail(), request.getId());

        // 检查手机号是否已存在
        checkPhoneExists(request.getPhoneCode(), request.getPhoneNumber(), request.getId());

        Instructor instructor = new Instructor();
        BeanUtils.copyProperties(request, instructor);

        // 更新讲师
        this.updateById(instructor);

        // 更新讲师类型关系
        if (CollUtil.isNotEmpty(request.getTypeIds())) {
            instructorTypeRelationService.updateInstructorTypeRelations(instructor.getId(), request.getTypeIds());
        }

        // 更新不可授课时间
        if (CollUtil.isNotEmpty(request.getUnavailableTimes())) {
            instructorUnavailableTimeService.updateUnavailableTimes(instructor.getId(), request.getUnavailableTimes());
        }


        // 更新讲师课程关系
        if (CollUtil.isNotEmpty(request.getCourseIds())) {
            instructorCourseRelationService.updateInstructorCourseRelations(instructor.getId(), request.getCourseIds());
        }

    }

    @Override
    public InstructorResponse getInstructorDetail(Long id) {
        Instructor instructor = this.getById(id);
        if (instructor == null || instructor.getIsDeleted()) {
            throw new IllegalArgumentException("讲师不存在");
        }

        InstructorResponse response = new InstructorResponse();
        BeanUtils.copyProperties(instructor, response);

        // 获取讲师类型
        List<InstructorTypeResponse> types = instructorDao.findTypesByInstructorIds(Collections.singletonList(id));
        response.setTypes(types);

        // 获取不可授课时间
        List<UnavailableTimeResponse> unavailableTimes = instructorDao.findUnavailableTimesByInstructorIds(Collections.singletonList(id));
        response.setUnavailableTimes(unavailableTimes);

        // 获取讲师课程
        List<CourseResponse> courses = instructorDao.findCoursesByInstructorIds(Collections.singletonList(id));
        response.setCourses(courses);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInstructor(Long id) {
        Instructor instructor = this.getById(id);
        if (instructor == null || instructor.getIsDeleted()) {
            throw new IllegalArgumentException("讲师不存在");
        }

        // 逻辑删除讲师
        instructor.setIsDeleted(true);
        this.updateById(instructor);
    }

    @Override
    public String importInstructor(MultipartFile file) {
        // 导入逻辑实现
        // 这里仅作为示例，实际实现需要根据业务需求进行开发
        return "导入成功";
    }

    @Override
    public void exportInstructor(HttpServletResponse response, InstructorListRequest param) {
        try {
            // 1. 查询数据（不分页）
            List<InstructorResponse> instructorList = instructorDao.findInstructorList(param);

            // 2. 处理查询结果和关联数据
            if (CollUtil.isNotEmpty(instructorList)) {
                // 3. 提取所有讲师的ID
                List<Long> instructorIds = instructorList.stream()
                        .map(InstructorResponse::getId)
                        .collect(Collectors.toList());

                // 4. 批量查询关联数据
                List<InstructorTypeResponse> allTypes = instructorDao.findTypesByInstructorIds(instructorIds);
                List<CourseResponse> allCourses = instructorDao.findCoursesByInstructorIds(instructorIds);
                List<UnavailableTimeResponse> allUnavailableTimes = instructorDao.findUnavailableTimesByInstructorIds(instructorIds);

                // 5. 将关联数据转换为Map，方便高效查找
                Map<Long, List<InstructorTypeResponse>> typeMap = allTypes.stream()
                        .collect(Collectors.groupingBy(InstructorTypeResponse::getInstructorId));

                Map<Long, List<CourseResponse>> courseMap = allCourses.stream()
                        .collect(Collectors.groupingBy(CourseResponse::getInstructorId));

                Map<Long, List<UnavailableTimeResponse>> unavailableTimeMap = allUnavailableTimes.stream()
                        .collect(Collectors.groupingBy(UnavailableTimeResponse::getInstructorId));

                // 6. 组装数据
                instructorList.forEach(instructor -> {
                    instructor.setTypes(typeMap.getOrDefault(instructor.getId(), Collections.emptyList()));
                    instructor.setCourses(courseMap.getOrDefault(instructor.getId(), Collections.emptyList()));
                    instructor.setUnavailableTimes(unavailableTimeMap.getOrDefault(instructor.getId(), Collections.emptyList()));
                });
            }

            // 7. 创建Excel导出对象
            String fileName = "讲师列表_" + System.currentTimeMillis() + ".xlsx";

            // 8. 设置响应头
            response.reset(); // 重置响应
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileNameEncoded = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncoded);

            // 9. 创建导出VO列表并转换数据
            List<InstructorExportVO> exportList = instructorList.stream().map(instructor -> {
                InstructorExportVO vo = new InstructorExportVO();
                vo.setId(instructor.getId());
                vo.setName(instructor.getName() != null ? instructor.getName() : "");
                vo.setEmail(instructor.getEmail() != null ? instructor.getEmail() : "");
                vo.setPhoneCode(instructor.getPhoneCode() != null ? instructor.getPhoneCode() : "");
                vo.setPhoneNumber(instructor.getPhoneNumber() != null ? instructor.getPhoneNumber() : "");

                // 处理类型
                if (CollUtil.isNotEmpty(instructor.getTypes())) {
                    vo.setTypes(instructor.getTypes().stream()
                            .map(InstructorTypeResponse::getName)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(", ")));
                } else {
                    vo.setTypes("");
                }

                // 处理课程
                if (CollUtil.isNotEmpty(instructor.getCourses())) {
                    vo.setCourses(instructor.getCourses().stream()
                            .map(CourseResponse::getName)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(", ")));
                } else {
                    vo.setCourses("");
                }

                return vo;
            }).collect(Collectors.toList());

            // 10. 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), InstructorExportVO.class)
                    .autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new HorizontalCellStyleStrategy(
                            ExcelStyleHandler.getHeadStyle(),
                            ExcelStyleHandler.getContentStyle()))
                    .sheet("讲师列表")
                    .doWrite(exportList);

        } catch (Exception e) {
            log.error("导出讲师列表失败", e);
            // 重置响应
            try {
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().println("{\"code\":500,\"message\":\"导出失败：" + e.getMessage() + "\"}");
                response.getWriter().flush();
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 检查邮箱是否已存在
     * @param email 邮箱
     * @param excludeId 排除的ID
     */
    private void checkEmailExists(String email, Long excludeId) {
        LambdaQueryWrapper<Instructor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Instructor::getEmail, email)
                .eq(Instructor::getIsDeleted, false);

        if (excludeId != null) {
            queryWrapper.ne(Instructor::getId, excludeId);
        }

        if (count(queryWrapper) > 0) {
            throw new IllegalArgumentException("邮箱已存在");
        }
    }

    /**
     * 检查手机号是否已存在
     * @param phoneCode 手机号国际区号
     * @param phoneNumber 手机号
     * @param excludeId 排除的ID
     */
    private void checkPhoneExists(String phoneCode, String phoneNumber, Long excludeId) {
        LambdaQueryWrapper<Instructor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Instructor::getPhoneCode, phoneCode)
                .eq(Instructor::getPhoneNumber, phoneNumber)
                .eq(Instructor::getIsDeleted, false);

        if (excludeId != null) {
            queryWrapper.ne(Instructor::getId, excludeId);
        }

        if (count(queryWrapper) > 0) {
            throw new IllegalArgumentException("手机号已存在");
        }
    }
}
