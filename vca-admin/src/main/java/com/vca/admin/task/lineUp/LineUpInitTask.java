package com.vca.admin.task.lineUp;

import com.vca.common.utils.DateUtil;
import com.vca.service.service.VcaLineUpService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class LineUpInitTask {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(LineUpInitTask.class);
    @Autowired
    private VcaLineUpService vcaLineUpService;

    private static final String SIXTY_MINUTE = "PT30M";
    private static final String THREE_MINUTE = "PT3M";

    @Scheduled(cron = "0 0 0 */1 * ?") //每天0点执行
    @SchedulerLock(name = "LineUpInitTask", lockAtMostFor = SIXTY_MINUTE, lockAtLeastFor = THREE_MINUTE)
    public void init() {
        logger.info("---LineUpInitTask task--- Execution Time - {}", DateUtil.nowDateTime());
        try {
            vcaLineUpService.initLineUp();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
