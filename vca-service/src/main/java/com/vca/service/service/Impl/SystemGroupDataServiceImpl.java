package com.vca.service.service.Impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.vca.common.constants.SysGroupDataConstants;
import com.vca.common.model.category.Category;
import com.vca.common.model.system.SystemGroupData;
import com.vca.common.request.PageParamRequest;
import com.vca.common.request.SystemFormItemCheckRequest;
import com.vca.common.request.SystemGroupDataRequest;
import com.vca.common.request.SystemGroupDataSearchRequest;
import com.vca.common.response.CourseTypeResponse;
import com.vca.common.utils.VcaUtil;
import com.vca.common.vo.ConfigDataVo;
import com.vca.service.dao.system.SystemGroupDataDao;
import com.vca.service.service.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SystemGroupDataServiceImpl 接口实现
 */
@Service
@Slf4j
public class SystemGroupDataServiceImpl extends ServiceImpl<SystemGroupDataDao, SystemGroupData> implements SystemGroupDataService {

    @Resource
    private SystemGroupDataDao dao;

    @Autowired
    private SystemFormTempService systemFormTempService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private VcaAboutService vcaAboutService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页类参数
     * @return List<SystemGroupData>
     */
    @Override
    public List<SystemGroupData> getList(SystemGroupDataSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 SystemGroupData 类的多条件查询
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        SystemGroupData model = new SystemGroupData();
        BeanUtils.copyProperties(request, model);
        lambdaQueryWrapper.setEntity(model);
        lambdaQueryWrapper.orderByDesc(SystemGroupData::getSort).orderByAsc(SystemGroupData::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 保存数据
     *
     * @param systemGroupDataRequest SystemGroupDataRequest 数据保存
     * @return Boolean
     */
    @Override
    public Boolean create(SystemGroupDataRequest systemGroupDataRequest) {
        //检测form表单，并且返回需要添加的数据
        systemFormTempService.checkForm(systemGroupDataRequest.getForm());

        SystemGroupData systemGroupData = new SystemGroupData();
        systemGroupData.setGid(systemGroupDataRequest.getGid());

        String value = JSONObject.toJSONString(systemGroupDataRequest.getForm());
        value = systemAttachmentService.clearPrefix(value);
        systemGroupData.setValue(value);
        systemGroupData.setSort(systemGroupDataRequest.getForm().getSort());
        systemGroupData.setStatus(systemGroupDataRequest.getForm().getStatus());
        return save(systemGroupData);
    }

    /**
     * 修改组合数据详情表
     *
     * @param id      integer id
     * @param request 修改参数
     * @return bool
     */
    @Override
    public Boolean update(Integer id, SystemGroupDataRequest request) {
        //检测form表单，并且返回需要添加的数据
        systemFormTempService.checkForm(request.getForm());

        SystemGroupData systemGroupData = new SystemGroupData();
        systemGroupData.setId(id);
        systemGroupData.setGid(request.getGid());

        String value = JSONObject.toJSONString(request.getForm());
        value = systemAttachmentService.clearPrefix(value);
        log.info("value-------------------------------->{}", value);
        systemGroupData.setValue(value);
        systemGroupData.setSort(request.getForm().getSort());
        systemGroupData.setStatus(request.getForm().getStatus());
        return updateById(systemGroupData);
    }

    /**
     * 通过gid获取列表 推荐二开使用
     *
     * @param gid Integer group id
     * @return List<T>
     */
    @Override
    public <T> List<T> getListByGid(Integer gid, Class<T> cls) {
        SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
        systemGroupDataSearchRequest.setGid(gid);
        systemGroupDataSearchRequest.setStatus(true);
        List<SystemGroupData> list = getList(systemGroupDataSearchRequest, new PageParamRequest());

        List<T> arrayList = new ArrayList<>();
        if (list.size() < 1) {
            return null;
        }

        for (SystemGroupData systemGroupData : list) {
            JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
            List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = VcaUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
            if (systemFormItemCheckRequestList.size() < 1) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();
            T t;
            for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
            }
            map.put("id", systemGroupData.getId());
            t = VcaUtil.mapToObj(map, cls);
            arrayList.add(t);
        }

        return arrayList;
    }

    /**
     * 通过gid获取列表
     * 查詢的是status为已开启的list
     *
     * @param gid Integer group id
     * @return List<HashMap < String, Object>>
     * <AUTHOR>
     * @since 2020-05-15
     */
    @Override
    public List<HashMap<String, Object>> getListMapByGid(Integer gid) {
        return getListMapByGid(gid,"");
    }

    @Override
    public List<HashMap<String, Object>> getListMapByGid(Integer gid, String language) {
        SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
        systemGroupDataSearchRequest.setGid(gid);
        systemGroupDataSearchRequest.setStatus(true);
        systemGroupDataSearchRequest.setIsDeleted(0);
        List<SystemGroupData> list = getList(systemGroupDataSearchRequest, new PageParamRequest());

        List<HashMap<String, Object>> arrayList = new ArrayList<>();
        if (list.size() < 1) {
            return null;
        }
        for (SystemGroupData systemGroupData : list) {
            JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
            if("en".equals(language)){
                List<ConfigDataVo> fields = JSONArray.parseArray(jsonObject.get("fields").toString(), ConfigDataVo.class);
                ConfigDataVo nameVo = null;
                ConfigDataVo nameEnVo = null;
                ConfigDataVo addressVo = null;
                ConfigDataVo addressEnVo = null;
                ConfigDataVo titleVo = null;
                ConfigDataVo titleEnVo = null;
                ConfigDataVo detailsVo = null;
                ConfigDataVo detailsEnVo = null;
                ConfigDataVo txtVo = null;
                ConfigDataVo txtEnVo = null;
                ConfigDataVo imageVo = null;
                ConfigDataVo imageEnVo = null;

                ConfigDataVo problemVo = null;
                ConfigDataVo answerVo = null;
                for (ConfigDataVo dataVo : fields) {
                    if ("name".equals(dataVo.getName())){
                        nameVo = dataVo;
                    }
                    if ("nameEn".equals(dataVo.getName())){
                        nameEnVo = dataVo;
                    }
                    if ("image".equals(dataVo.getName())){
                        imageVo = dataVo;
                    }
                    if ("imageEn".equals(dataVo.getName())){
                        imageEnVo = dataVo;
                    }

                    if ("address".equals(dataVo.getName())){
                        addressVo = dataVo;
                    }
                    if ("addressEn".equals(dataVo.getName())){
                        addressEnVo = dataVo;
                    }

                    if ("title".equals(dataVo.getName())){
                        titleVo = dataVo;
                    }
                    if ("problem".equals(dataVo.getName())){
                        problemVo = dataVo;
                    }
                    if ("field101".equals(dataVo.getName())){
                        titleEnVo = dataVo;
                    }

                    if ("details".equals(dataVo.getName())){
                        detailsVo = dataVo;
                    }
                    if ("answer".equals(dataVo.getName())){
                        answerVo = dataVo;
                    }
                    if ("field102".equals(dataVo.getName())){
                        detailsEnVo = dataVo;
                    }

                    //System.out.println(dataVo+"==============");
                    if ("txt".equals(dataVo.getName())){
                        txtVo = dataVo;
                    }
                    if ("txtEn".equals(dataVo.getName())){
                        txtEnVo = dataVo;
                    }

                    if ("updateTime".equals(dataVo.getName())){
                        System.out.println(dataVo+"*************");
                        String val = dataVo.getValue().replace("月","");
                        String[] strArray = val.split("年");
                        int month = Integer.parseInt(strArray[1]);
                        String month_en = "";
                        if(month == 1){
                            month_en = "January ";
                        }else if(month == 2){
                            month_en = "February ";
                        }else if(month == 3){
                            month_en = "March ";
                        }else if(month == 4){
                            month_en = "April ";
                        }else if(month == 5){
                            month_en = "May ";
                        }else if(month == 6){
                            month_en = "June ";
                        }else if(month == 7){
                            month_en = "July ";
                        }else if(month == 8){
                            month_en = "August ";
                        }else if(month == 9){
                            month_en = "September ";
                        }else if(month == 10){
                            month_en = "October ";
                        }else if(month == 11){
                            month_en = "November ";
                        }else if(month == 12){
                            month_en = "December ";
                        }
                        dataVo.setValue(month_en+strArray[0]);
                        System.out.println(dataVo+"---------------");
                    }

                }
                if (Objects.nonNull(nameVo) && Objects.nonNull(nameEnVo)){
                    nameVo.setValue(nameEnVo.getValue());
                }
                if (Objects.nonNull(imageVo) && Objects.nonNull(imageEnVo)){
                    imageVo.setValue(imageEnVo.getValue());
                }
                if (Objects.nonNull(addressVo) && Objects.nonNull(addressEnVo)){
                    addressVo.setValue(addressEnVo.getValue());
                }

                if (Objects.nonNull(txtVo) && Objects.nonNull(txtEnVo)){
                    txtVo.setValue(txtEnVo.getValue());
                }

                if (Objects.nonNull(titleVo) && Objects.nonNull(titleEnVo)){
                    titleVo.setValue(titleEnVo.getValue());
                }
                if (Objects.nonNull(detailsVo) && Objects.nonNull(detailsEnVo)){
                    detailsVo.setValue(detailsEnVo.getValue());
                }

                if (Objects.nonNull(problemVo) && Objects.nonNull(titleEnVo)){
                    problemVo.setValue(titleEnVo.getValue());
                }
                if (Objects.nonNull(answerVo) && Objects.nonNull(detailsEnVo)){
                    answerVo.setValue(detailsEnVo.getValue());
                }


                String fieldsJson = JSON.toJSONString(fields);
                jsonObject.put("fields",fieldsJson);
            }


            List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = VcaUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
            if (systemFormItemCheckRequestList.size() < 1) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();

            for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
            }
            map.put("id", systemGroupData.getId());
            map.put("status", systemGroupData.getStatus());
            map.put("sort", systemGroupData.getSort());
//            if("en".equals(language)){
//                System.out.println(jsonObject.get("fields"));
//
//                JSONArray fields = JSONArray.parseArray(jsonObject.get("fields").toString());
//                fields.forEach(e->{
//                    JSONObject jsonObject1 = JSONObject.parseObject(e.toString());
//                    jsonObject1.put("name",jsonObject1.get("nameEn"));
//
//                });
//            }
            arrayList.add(map);
        }

        return arrayList;
    }

    @Override
    public List<HashMap<String, Object>> getListMapByGidAll(String language,Integer gid, Boolean isDeleted) {
        SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
        systemGroupDataSearchRequest.setGid(gid);
        if (isDeleted){
            systemGroupDataSearchRequest.setIsDeleted(0);
        }
//        systemGroupDataSearchRequest.setStatus(true);
        List<SystemGroupData> list = getList(systemGroupDataSearchRequest, new PageParamRequest());

        List<HashMap<String, Object>> arrayList = new ArrayList<>();
        if (list.size() < 1) {
            return null;
        }

        for (SystemGroupData systemGroupData : list) {
            JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());

            if("en".equals(language)){
                List<ConfigDataVo> fields = JSONArray.parseArray(jsonObject.get("fields").toString(), ConfigDataVo.class);
                ConfigDataVo courseLanguageVo = null;
                ConfigDataVo courseLanguageEnVo = null;
                for (ConfigDataVo dataVo : fields) {
                    if ("courseLanguage".equals(dataVo.getName())){
                        courseLanguageVo = dataVo;
                    }
                    if ("courseLanguageEn".equals(dataVo.getName())){
                        courseLanguageEnVo = dataVo;

                    }
                }
                if (Objects.nonNull(courseLanguageVo) && Objects.nonNull(courseLanguageEnVo)){
                    courseLanguageVo.setValue(courseLanguageEnVo.getValue());
                }



                String fieldsJson = JSON.toJSONString(fields);
                jsonObject.put("fields",fieldsJson);
            }

            List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = VcaUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
            if (systemFormItemCheckRequestList.size() < 1) {
                continue;
            }
            /*
            List<ConfigDataVo> fields = JSONArray.parseArray(jsonObject.get("fields").toString(), ConfigDataVo.class);
            ConfigDataVo courseLanguageVo = null;
            ConfigDataVo courseLanguageEnVo = null;
            for (ConfigDataVo dataVo : fields) {
                if ("courseLanguage".equals(dataVo.getName())){
                    courseLanguageVo = dataVo;
                }
                if ("courseLanguageEn".equals(dataVo.getName())){
                    courseLanguageEnVo = dataVo;
                }
            }
            if (Objects.nonNull(courseLanguageVo) && Objects.nonNull(courseLanguageEnVo)){
                courseLanguageVo.setValue(courseLanguageEnVo.getValue());
            }

*/

            HashMap<String, Object> map = new HashMap<>();
            for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
            }
            System.out.println(systemFormItemCheckRequestList+"==================");



            map.put("id", systemGroupData.getId());
            map.put("status", systemGroupData.getStatus());
            map.put("sort", systemGroupData.getSort());
            arrayList.add(map);
        }

        return arrayList;
    }

    @Override
    public HashMap<String, Object> getMapByGid(Integer gid,Integer keyId){
        SystemGroupData systemGroupData = new SystemGroupData();
        systemGroupData.setGid(gid);
//        systemGroupData.setStatus(true);
        systemGroupData.setId(keyId);
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.setEntity(systemGroupData);
        SystemGroupData groupData = dao.selectOne(lambdaQueryWrapper);
        JSONObject jsonObject = JSONObject.parseObject(groupData.getValue());
        List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = VcaUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
        if (systemFormItemCheckRequestList.size() < 1) {
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
            map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
        }
        map.put("id", systemGroupData.getId());
        map.put("status", systemGroupData.getStatus());
        map.put("sort", systemGroupData.getSort());
        return map;
    }

    /**
     * 通过gid获取列表
     *
     * @param groupDataId Integer group id
     * @return <T>
     */
    @Override
    public <T> T getNormalInfo(Integer groupDataId, Class<T> cls) {
        SystemGroupData systemGroupData = getById(groupDataId);
        if (null == systemGroupData || !systemGroupData.getStatus()) {
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
        List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = VcaUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
        if (systemFormItemCheckRequestList.size() < 1) {
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        T t;
        for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
            map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
        }
        map.put("id", systemGroupData.getId());
        t = VcaUtil.mapToObj(map, cls);

        return t;
    }

    /**
     * 获取个人中心菜单
     *
     * @return HashMap<String, Object>
     */
    @Override
    public HashMap<String, Object> getMenuUser() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("routine_my_menus", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_USER_CENTER_MENU));
        map.put("routine_my_banner", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_USER_CENTER_BANNER));
        return map;
    }

    /**
     * @Description:获取首页配置
     * @Author: chenBing
     * @Date: 2022/9/19
     */
    @Override
    public HashMap<String, Object> getIndexConfig() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("routine_index_config", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_HOME_PAGE_CONFIGURATION));
        return map;
    }

    /**
     * @Description:获取课程过滤参数
     * @Author: chenBing
     * @Date: 2022/9/19
     */
    @Override
    public HashMap<String, Object> getCourseConfig(String language) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("routine_course_language", getListMapByGidAll(language,SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_LANGUAGE,true));
        map.put("routine_course_duration", getListMapByGidAll(language,SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_DURATION,true));
        map.put("routine_course_price", getListMapByGidAll(language,SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_PRICE,true));
        LambdaQueryWrapper<Category> lqw = new LambdaQueryWrapper<Category>()
                .eq(Category::getType, 8)
                .eq(Category::getIsDel,0)
                .ne(Category::getPid, 0)
                .ne(Category::getId,840)
                .orderByAsc(Category::getSort);
        List<Category> categories = categoryService.list(lqw);
        List<CourseTypeResponse> courseTypeResponseList = new ArrayList<>();
        categories.forEach(c -> {
            CourseTypeResponse courseTypeResponse = new CourseTypeResponse();
            courseTypeResponse.setId(Long.valueOf(c.getId()));

            if("en".equals(language)){
                courseTypeResponse.setCourseTypeName(c.getNameEn());
            }else{
                courseTypeResponse.setCourseTypeName(c.getName());
            }

            courseTypeResponse.setStatus(c.getStatus());
            courseTypeResponse.setLabelColor(c.getLabelColor());
            courseTypeResponse.setSort(c.getSort());
            courseTypeResponse.setPid(Long.valueOf(c.getPid()));
            courseTypeResponseList.add(courseTypeResponse);
        });
        map.put("routine_course_category",courseTypeResponseList);
        return map;
    }

    /**
     * @Description:获取首页配置信息
     * @Author: Li
     * @Date: 2022/10/25 10:57
     */
    @Override
    public HashMap<String, Object> getConfig(String language) {
        HashMap<String, Object> map = new HashMap<>();
        List<HashMap<String, Object>> listMapByGid = getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_HOME_BANNER);
        if (ObjectUtil.isNotEmpty(listMapByGid)){
            listMapByGid.forEach(e->{
                List<Object> list = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(e.get("titleOne"))){
                    if("en".equals(language)){
                        list.add(e.get("titleOneEn"));
                        e.remove("titleOneEn");
                    }else{
                        list.add(e.get("titleOne"));
                        e.remove("titleOne");
                    }
                }
                if (ObjectUtil.isNotEmpty(e.get("titleTwo"))){
                    if("en".equals(language)){
                        list.add(e.get("titleTwoEn"));
                        e.remove("titleTwoEn");
                    } else{
                        list.add(e.get("titleTwo"));
                        e.remove("titleTwo");
                    }
                }
                if (ObjectUtil.isNotEmpty(e.get("titleThree"))){
                    if("en".equals(language)){
                        list.add(e.get("titleThreeEn"));
                        e.remove("titleThreeEn");
                    }else{
                        list.add(e.get("titleThree"));
                        e.remove("titleThree");
                    }
                }
                if (ObjectUtil.isNotEmpty(e.get("isTitleDisplayed"))){
                    e.put("isTitleDisplayed",Boolean.valueOf(e.get("isTitleDisplayed").toString()));
                }
                if (ObjectUtil.isNotEmpty(e.get("isButtonDisplayed"))){
                    e.put("isButtonDisplayed",Boolean.valueOf(e.get("isButtonDisplayed").toString()));
                }

                if (ObjectUtil.isNotEmpty(e.get("buttonText"))){
                    if("en".equals(language)){
                        e.put("buttonText",e.get("buttonTextEn"));
                    }
                }
                if (ObjectUtil.isNotEmpty(e.get("text"))){
                    if("en".equals(language)){
                        e.put("text",e.get("textEn"));
                    }
                }
                e.put("title",list);
            });
        }
        System.out.println(listMapByGid+"=========");
        map.put("homeBanner",listMapByGid);
        map.put("homeBackground", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_HOME_BACKGROUND));
        map.put("homeDynamic", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_HOME_PAGE_CONFIGURATION,language));
        if("en".equals(language)){
            map.put("shareTitle",systemConfigService.getValueByKey("routine_title_en"));
        }else{
            map.put("shareTitle",systemConfigService.getValueByKey("routine_title"));
        }
        map.put("shareImage",systemConfigService.getValueByKey("image"));
        return map;
    }

    /**
     * 获得课程后台配置
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/11 10:39
     */
    @Override
    public Map<String,List<HashMap<String, Object>>> getCourseAdminConfig() {
        Map<String,List<HashMap<String, Object>>> map = new HashMap();
        map.put("durationList", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_DURATION));
        map.put("priceList", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_PRICE));
        map.put("addressList", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_ADDRESS));
        return map;
    }

    /**
     * 获得课程后台配置
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/11 10:39
     */
    @Override
    public Map<String, List<HashMap<String, Object>>> getCourseAdminConfigAll() {
        Map<String,List<HashMap<String, Object>>> map = new HashMap();
        map.put("durationList", getListMapByGidAll("",SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_DURATION,false));
        map.put("priceList", getListMapByGidAll("",SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_PRICE,false));
        map.put("addressList", getListMapByGidAll("",SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_ADDRESS,false));
        return map;
    }

    /**
     * 获得课程后台配置
     * @return
     * <AUTHOR>
     * @date 2022/11/11 10:39
     */
    @Override
    public Map<String, List> getCourseSchedulingAdminConfig() {
        Map<String,List> map = new HashMap<>();
        map.put("languageList", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_CLIENT_COURSE_LANGUAGE));
        return map;
    }

    /**
     * 获取礼品卡编号
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/24 10:14
     */
    @Override
    public Map<String, List> getCardList() {
        Map<String,List> map = new HashMap<>();
        map.put("cardList", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_HOME_CARD));
        return map;
    }

    /**
     * @description 获取关于配置信息和关于列表
     * <AUTHOR>
     * @date 2022/12/9 14:38
     * @return java.util.Map<java.lang.String,java.util.List>
     */
    @Override
    public Map<String,Object> getAboutList(String language) {
        Map<String,Object> map = new HashMap<>();
        List<String> images=new ArrayList<>();
        List<HashMap<String, Object>> mapByGid = getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_ABOUT_CAROUSEL);
        mapByGid.forEach(e->{
            if (ObjectUtil.isNotEmpty(e.get("image"))) {
                images.add(e.get("image").toString());

            }
            if (ObjectUtil.isNotEmpty(e.get("video"))) {
                images.add(e.get("video").toString());
            }
        });
        List<HashMap<String, Object>> base = getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_ABOUT_BASE);
        base.forEach(e->{
            if("en".equals(language)){
                //System.out.println(e.get("titleEn")+"==========");
                //System.out.println(e.get("buttonEn")+"-------------");
                e.put("title",e.get("titleEn"));
                e.put("button",e.get("buttonEn"));
                e.put("introduction",e.get("introductionEn"));
            }

            String link = e.get("link").toString();
            if (link.contains("id=")) {
                e.put("id",link.substring(link.indexOf("=") + 1));
            }
            e.put("carouselMap",images);
        });
        //System.out.println("==================");
        //System.out.println(base.get(0));
        map.put("base",base.get(0));
        List<HashMap<String, Object>> listMapByGid = getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_ABOUT_CENTRE,language);
        Map<String, List<HashMap<String, Object>>> listMap = listMapByGid.stream().collect(Collectors.groupingBy(this::customKey));
        List<Object> list=new ArrayList<>();
        listMap.forEach((k,v)->{
            Map<String, Object> lMap = new HashMap<>();
            lMap.put("type",k);
            lMap.put("centreList",v);
            list.add(lMap);
        });
        map.put("centre",listMapByGid);
        return map;
    }

    private  String customKey(Map<String,Object> map){
        return map.get("type").toString();
    }

    /**
     * @description 常见问题
     * <AUTHOR>
     * @date 2022/12/9 15:16
     * @return java.util.Map<java.lang.String,java.util.List>
     */
    @Override
    public Map<String,List> getFqa(String language) {
        Map<String,List> map = new HashMap<>();
        map.put("FQA",getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_FQA,language));
        return map;
    }

    /**
     * @return java.util.Map<java.lang.String, java.util.List>
     * @description 小程序隐私条款
     * <AUTHOR>
     * @date 2022/12/9 15:26
     */
    @Override
    public Map<String, List> getPrivacyPolicy(String language) {
        Map<String,List> map = new HashMap<>();
        map.put("base",getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_BASE,language));
        map.put("privacyPolicy",getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_PRIVACY_POLICY,language));
        return map;
    }

    /**
     * 获取列表通过gid
     *
     * @param gid gid
     * @return 列表
     */
    @Override
    public List<SystemGroupData> findListByGid(Integer gid) {
        LambdaQueryWrapper<SystemGroupData> lqw = Wrappers.lambdaQuery();
        lqw.eq(SystemGroupData::getGid, gid);
        lqw.orderByAsc(SystemGroupData::getSort).orderByAsc(SystemGroupData::getId);
        return dao.selectList(lqw);
    }

    /**
     * @param id
     * @return boolean
     * @description 根据id去逻辑删除
     * @methodName del
     * * @param id
     * <AUTHOR>
     * @date 2023/1/3 14:34
     */
    @Override
    public boolean del(Integer id) {
        SystemGroupData systemGroupData = dao.selectOne(new LambdaQueryWrapper<SystemGroupData>().eq(SystemGroupData::getId, id));
        systemGroupData.setIsDeleted(1);
        return transactionTemplate.execute(e->{
            dao.updateById(systemGroupData);
            return Boolean.TRUE;
        });
    }

    /**
     * @description 条款细则
     * <AUTHOR>
     * @date 2023/2/16 11:06
     * @return {@link Map}<{@link String},{@link Object}>
     */
    @Override
    public Map<String, Object> getTermsConditions(String language) {
        Map<String,Object> map = new HashMap<>();
        map.put("termsConditions",getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_TERMS_CONDITIONS,language));
        return map;
    }

    @Override
    public Map<String, Object> birthstone(String language) {
        Map<String,Object> map = new HashMap<>();
        Map<String, List<HashMap<String, Object>>> listMap = getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_BIRTHSTONE,language).stream().collect(Collectors.groupingBy(this::customMonth));
        if(language.equals("en")){
            listMap.forEach((k,v)->{
                v.forEach(e->{
                    e.put("name",e.get("field101"));
                    e.put("image",e.get("imageEN"));
                });
            });
        }
        map.put("birthstone",listMap);
        return map;
    }

    private  String customMonth(Map<String,Object> map){
        return map.get("month").toString();
    }

    /**
     * 删除通过gid
     *
     * @param gid gid
     * @return Boolean
     */
    @Override
    public Boolean deleteByGid(Integer gid) {
        LambdaUpdateWrapper<SystemGroupData> luw = Wrappers.lambdaUpdate();
        luw.eq(SystemGroupData::getGid, gid);
        int delete = dao.delete(luw);
        if (delete > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}

