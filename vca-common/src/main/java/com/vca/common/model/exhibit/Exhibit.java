package com.vca.common.model.exhibit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName Exhibit
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2025/4/2 15:04
 **/
@Data
@TableName("vca_exhibit")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VcaExhibit对象", description="展品表")
public class Exhibit implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private int floorNo;
    private String floor;
    //showroom_name
    private String showroom;
    private String showroomName;
    //carousel_img

    //list_img
    private String listImg;
    //exhibit_name
    private String exhibitNo;
    private String exhibitName;
    //exhibit_carousel_img
    private String exhibitCarouselImg;


    //exhibit_audio
    private String exhibitAudio;
    private String exhibitCarouselImgS;
    //exhibit_source
    private String exhibitSource;
    private String exhibitCarouselImgSL;
    //exhibit_info
    private String exhibitInfo;
    private String carouselImg;
    private Date createTime;
    private Date updateTime;



}
