# VCA 相关配置
vca:
  version: VCA-JAVA-KY-v1.3.4 # 当前代码版本
  domain: #配合swagger使用 # 待部署域名
  wechat-api-url:  #请求微信接口中专服务器
  wechat-js-api-debug: false #微信js api系列是否开启调试模式
  wechat-js-api-beta: true #微信js api是否是beta版本
  asyncConfig: false #是否同步config表数据到redis
  asyncWeChatProgramTempList: false #是否同步小程序公共模板库
  imagePath: /localfile/ # 服务器图片路径配置 斜杠结尾
  active: /uat-api
  serverName: https://cms-test.lecolevancleefarpels.cn
  proxy:
    disable: true
  sms:
    enable: true
    apiUrl: https://api.preprod.rcdc.richemont.cn/rwf-notification/api/v1/message
    auth: lecolevuser:DO4Y6Ojv9n9pqhvk6ucqN5iaQaiIGoVC
  obs:
    name: ods_data_uat
  #  配置的环境
  environment:
    active: uat

server:
  port: 20010

spring:
  profiles:
    #  配置的环境
    active: uat
  jackson:
    locale: zh_CN
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    #  数据库配置
  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: *******************************************************************************************************************************
    username: vcawebchat_admin
    password: sq1nZ9nZKm&b8vgX
  redis:
    host: r-6nn5f1c57f582394.redis.rds.aliyuncs.com #地址
    port: 6379 #端口R
    password: vcawebchat_admin:K3QgyYXr)LCp0FxQUhX(
    timeout: 10000 # 连接超时时间（毫秒）
    database: 6 #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
debug: true
logging:
  level:
    io.swagger.*: error
    com.vca.admin: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml
  file:
    path: ./vca_log

# mybatis 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*/*Mapper.xml #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  typeAliasesPackage: com.zbkj.**.model
  # 配置slq打印日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      #      logic-delete-field: isDel  #全局逻辑删除字段值 3.3.0开始支持，详情看下面。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
#swagger 配置
swagger:
  basic:
    enable: false #是否开启
    check: true #是否打开验证
    username: lecole #访问swagger的账号
    password: lecole2022@ #访问swagger的密码

xss:
  xss_key:
  xss_replacedstring:
env:
  version: trial
invoice:
  url: http://richemont-test.xforceplus.com
wx:
  miniapp:
    isProxy: true
    httpProxyHost: zproxy-sh.cn.ali.rccad.net
    httpProxyPort: 80
    configs:
      - appid: wxbd3b07b6a1ece617
        secret: 7a68c6cedbe84c2f149946ad9c59bd4e
        token: kjhkj7898fks9623jj7fsg8
        aesKey: xZVNW77UcoBrRdYA99KGN4yy9iJUOAAX33PM9aBkUke
        msgDataFormat: JSON
