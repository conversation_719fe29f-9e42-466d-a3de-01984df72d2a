package com.vca.common.model.exhibition;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 讲座详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
@TableName("vca_exhibition_info")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VcaExhibitionInfo对象", description="讲座详情")
public class ExhibitionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "展览详情主键id")
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "展览主键id")
    private Long exhibitionId;

    @ApiModelProperty(value = "展品详情")
    private String details;

    @ApiModelProperty(value = "展品英文详情")
    private String detailsEn;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


}
