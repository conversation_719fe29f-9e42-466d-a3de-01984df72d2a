package com.vca.common.model.sf;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ContactInfo对象", description="联系信息")
public class ContactInfo implements Serializable {

    private static final long serialVersionUID = -5408753011724750307L;

    @ApiModelProperty(value = "地址类型： 1，寄件方信息 2，到件方信息")
    @NotBlank(message = "地址类型不能为空")
    private Integer contactType;

    @ApiModelProperty(value = "公司名称")
    private String company;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系电话")
    private String tel;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "城市代码或国家代码")
    private String zoneCode;

    @ApiModelProperty(value = "国家或地区2位代码")
    private String country;

    @ApiModelProperty(value = "所在省级行政区名称")
    private String province;

    @ApiModelProperty(value = "所在地级行政区名称")
    private String city;

    @ApiModelProperty(value = "所在县/区级行政区名称")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String postCode;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "税号")
    private String taxNo;

}
