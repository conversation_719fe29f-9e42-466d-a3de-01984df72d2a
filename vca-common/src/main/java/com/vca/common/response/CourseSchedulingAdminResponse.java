package com.vca.common.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;

/**
 * <p>
 * 课程排期后台管理Response
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14 17:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CourseSchedulingAdminResponse对象", description="课程排期后台管理Response")
public class CourseSchedulingAdminResponse implements Serializable {

    private static final long serialVersionUID = 4100726085093764577L;

    @ApiModelProperty(value = "排课主键id")
    private Long id;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "课程名称")
    private String name;

    @ApiModelProperty(value = "课程封面图")
    private String cover;

    @ApiModelProperty(value = "标签颜色")
    private String labelColor;

    @ApiModelProperty(value = "课程封面图 统一字段")
    private String coverImage;

    @ApiModelProperty(value = "排课日期")
    private String schedulingDate;

    @ApiModelProperty(value = "排课当天开始时间")
    private String schedulingStartTime;

    @ApiModelProperty(value = "排课当天结束时间")
    private String schedulingEndTime;

    @ApiModelProperty(value = "课程语言id")
    private Integer courseLanguageId;

    @ApiModelProperty(value = "课程语言")
    private String courseLanguage;

    @ApiModelProperty(value = "课程价格ID")
    private Integer priceId;

    @ApiModelProperty(value = "席位")
    private Integer seatCount;

    @ApiModelProperty(value = "外部限量")
    private Integer externalLimit;

    @ApiModelProperty(value = "外部预约")
    private Integer externalAppointment;

    @ApiModelProperty(value = "外部上课人数")
    private Integer externalStudentsNumber;

    @ApiModelProperty(value = "内部限量")
    private Integer internalLimit;

    @ApiModelProperty(value = "内部预约")
    private Integer internalAppointment;

    @ApiModelProperty(value = "内部上课人数")
    private Integer internalStudentsNumber;

    @ApiModelProperty(value = "预约状态")
    private Integer status;

    @ApiModelProperty(value = "课程状态 0=已结束 1=进行中 2=未开始 3=已取消")
    private Integer schedulingStatus;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "课程地址")
    @TableField(exist = false)
    private HashMap<String, Object> courseAddress;

    @ApiModelProperty(value = "课程语言")
    @TableField(exist = false)
    private String courseLanguageName;

    @ApiModelProperty(value = "剩余席位")
    @TableField(exist = false)
    private Integer remainingSeatCount;

    @ApiModelProperty(value = "课程价格")
    @TableField(exist = false)
    private String price;

    @ApiModelProperty("取消原因")
    private String cancellationReasons;
}
