package com.vca.common.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.vca.common.vo.CourseTypeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品类型响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ProductTypeAdminResponse对象", description="商品类型响应对象")
public class ProductTypeAdminResponse  implements Serializable {

    private static final long serialVersionUID = -4104077738634907356L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "父级ID")
    private Integer pid;

    @ApiModelProperty(value = "商品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "英文商品类型名称")
    private String productTypeNameEn;

    @ApiModelProperty(value = "标签颜色")
    private String labelColor;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @JsonInclude(JsonInclude.Include.NON_EMPTY) //属性为 空（""）[] 或者为 NULL 都不序列化
    private List<ProductTypeAdminResponse> child = new ArrayList<>();
}
