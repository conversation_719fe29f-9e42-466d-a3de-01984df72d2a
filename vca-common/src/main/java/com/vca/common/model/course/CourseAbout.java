package com.vca.common.model.course;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 相关课程表（课程）
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@TableName("vca_course_about")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CourseAbout对象", description="相关课程表（课程）")
public class CourseAbout implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "相关课程表主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "关联课程主键id（课程ID/套课ID/讲座ID/展览ID/商品ID）")
    private Long aboutMainId;

    @ApiModelProperty(value = "0 = 课程 1= 套课 2=讲座 3=展览 4=商品")
    private Integer typeMainCourse;

    @ApiModelProperty(value = "课程主键id（课程ID/套课ID/讲座ID/展览ID/商品ID）")
    private Long aboutId;

    @ApiModelProperty(value = "类型  1=相关课程 2=可能喜欢 3=相关出版物")
    private Integer type;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "0 = 课程 1= taoKe 2=讲座 3=展览 4=商品")
    private Integer typeCourse;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;


}
