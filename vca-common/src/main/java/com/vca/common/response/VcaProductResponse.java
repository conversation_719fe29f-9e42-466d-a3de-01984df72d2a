package com.vca.common.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.vca.common.model.vca_product.VcaProductAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VcaProductResponse implements Serializable {

    @ApiModelProperty(value = "商品主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品类型ID")
    private Integer cid;

    @ApiModelProperty(value = "类型名称")
    private String categoryName;

    @ApiModelProperty(value = "类型名称")
    private String courseType;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "英文商品名称")
    private String nameEn;

    @ApiModelProperty(value = "面值（录入的商品非礼品卡时，不需要填写面值）")
    private String faceValue;

    @ApiModelProperty(value = "商品简介")
    private String introduction;

    @ApiModelProperty(value = "英文商品简介")
    private String introductionEn;

    @ApiModelProperty(value = "商品封面图")
    private String coverImage;

    @ApiModelProperty(value = "商品轮播图")
    private String carouselMap;

    @ApiModelProperty(value = "主图视频")
    private String mainPictureVideo;

    @ApiModelProperty(value = "分享海报")
    private String sharePoster;

    @ApiModelProperty(value = "商品图片")
    private String image;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "ISBN编号")
    private String isbnCode;

    @ApiModelProperty(value = "规格 0单 1多")
    private Boolean specType;

    @ApiModelProperty(value = "销量")
    private Integer sales;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态（0：未上架，1：上架）")
    private Boolean isShow;

    @ApiModelProperty(value = "是否回收站")
    private Integer isRecycle;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "添加时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "使用类型 1 全场通用, 2 课程券, 3 分类券")
    private Boolean useType;

    @ApiModelProperty(value = "所属课程id / 分类id")
    private String primaryKey;

    @ApiModelProperty(value = "有效期")
    private Date endTime;

    @ApiModelProperty(value = "商品属性")
    private List<VcaProductAttr> attr;

    @ApiModelProperty(value = "商品属性详情")
    private List<VcaProductAttrValueResponse> attrValue;
//
//    @ApiModelProperty(value = "管理端用于映射attrResults")
//    private List<HashMap<String,Object>> attrValues;

    @ApiModelProperty(value = "商品描述")
    private String content;

    @ApiModelProperty(value = "收藏数量")
    private Integer collectCount;

}
