package com.vca.common.model.talk;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 讲座表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
@TableName("vca_talk")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "Talk对象", description = "讲座表")
public class Talk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "讲座类型 1=线下讲座 2=线上讲座")
    private Integer talkType;

    @ApiModelProperty(value = "讲座名称")
    private String name;

    @ApiModelProperty(value = "讲座英文名称")
    private String nameEn;

    @ApiModelProperty(value = "是否付费  0=免费  1=付费")
    private Integer isPay;

    @ApiModelProperty(value = "讲座价格")
    private BigDecimal price;

    @ApiModelProperty(value = "讲座时长")
    private String duration;

    @ApiModelProperty(value = "讲座时长(EN)")
    private String durationEn;

    @ApiModelProperty(value = "观看平台")
    private String viewingPlatformName;

    @ApiModelProperty(value = "英文观看平台")
    private String viewingPlatformNameEn;

    @ApiModelProperty(value = "讲座简介")
    private String introduction;

    @ApiModelProperty(value = "讲座英文简介")
    private String introductionEn;

    @ApiModelProperty(value = "讲座地址id")
    private Integer addressId;

    @ApiModelProperty(value = "讲座封面图")
    private String coverImage;

    @ApiModelProperty(value = "讲座轮播图")
    private String carouselMap;

    @ApiModelProperty(value = "主图视频")
    private String mainPictureVideo;

    @ApiModelProperty(value = "分享海报")
    private String sharePoster;

    @ApiModelProperty(value = "限量")
    private Integer quota;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value="跳转链接")
    private String link;

    @ApiModelProperty(value = "是否加入回收站")
    private Integer isRecycle;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "视频号ID")
    private String channelsId;

    @ApiModelProperty(value = "视频id")
    private String videoId;

}
