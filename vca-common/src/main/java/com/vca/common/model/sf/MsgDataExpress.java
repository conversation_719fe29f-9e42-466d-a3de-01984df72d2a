package com.vca.common.model.sf;

import com.alibaba.fastjson.JSONObject;
import com.qiniu.util.Json;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MsgDataExpress对象", description="扩展字段说明信息")
public class MsgDataExpress implements Serializable {
    private static final long serialVersionUID = 1950134317378513840L;

    @ApiModelProperty(value = "模板编码")
    private String templateCode;

    @ApiModelProperty(value = "业务数据")
    private List<Documents> documents;

    @ApiModelProperty(value = "版本号，传固定值:2.0")
    private String version;

    @ApiModelProperty(value = "生成面单文件格式")
    private String fileType;

    @ApiModelProperty(value = "true: 同步,false: 异步,默认异步")
    private Boolean sync;

    @ApiModelProperty(value = "自定义模板必须是已发布的，且规格要和需要打印的模板对应")
    private String customTemplateCode;
    @ApiModelProperty(value = "扩展字段")
    private JSONObject extJson;
}
