package com.vca.common.model.joaillerie;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @ClassName Joaillerie
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2024/10/18 11:25
 **/
@Data
@TableName("vca_joaillerie")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VcaJoaillerie对象", description="珠宝表")
public class Joaillerie {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;
        private String banner;
        private String coverImg;
        private String coverTitle;
        private String coverTitleEn;
        private String type;
        private String des;
        private String desEn;
        private String media;
        private String mediaEn;
        private String mediaTime;
        private String mediaTimeEn;
        private String title;
        private String titleEn;
        private String content;
        private String contentEn;
        private String status;
        private int sortNo;
        private Date createTime;
        private Date updateTime;

}
