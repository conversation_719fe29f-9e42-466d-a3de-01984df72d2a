package com.vca.common.request;

import com.vca.common.exception.VcaException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/22   17:43
 */
@Data
public class ComputedOrderCommonPriceRequest {

    @ApiModelProperty(value = "预下单订单号")
    private String preOrderNo;

    @ApiModelProperty(value = "课程/套课/讲座/展览计算价格参数")
    private CourseComputedPriceDetail courseComputedPriceDetail;

    @ApiModelProperty(value = "商品计算价格参数")
    private List<ShopComputedPriceDetail> shopComputedPriceDetails;

    @Data
    public static class ShopComputedPriceDetail{
        @ApiModelProperty(value = "购物车编号，购物车预下单时必填")
        private Long shoppingCartId;

        @ApiModelProperty(value = "商品的主键ID")
        @NotBlank(message = "商品的主键ID不能为空")
        private Integer productId;

        @ApiModelProperty(value = "商品规格属性id（立即购买、活动购买必填）")
        private Integer attrValueId;

        @ApiModelProperty(value = "商品数量")
        private Integer count;

//        @ApiModelProperty(value = "商品价格")
//        private BigDecimal price;
//
//        @ApiModelProperty(value = "商品名称")
//        private String name;
//
//        @ApiModelProperty(value = "类型名称")
//        private String type;
//
//        @ApiModelProperty(value = "类型标签颜色")
//        private String labelColor;
    }


    @Data
    public static class CourseComputedPriceDetail {

        @ApiModelProperty(value = "礼品卡ID")
        private Integer cardId;

        @ApiModelProperty(value = "是否需要发票")
        private Boolean invoiceRequired;

        @ApiModelProperty(value = "预约人数")
        @Range(max = 4, message = "最多可预约四人")
        private Integer appointmentsCount;

//        @ApiModelProperty(value = "赠送他人信息")
//        private List<HashMap<String, String>> giveAwayToOthersMap;

        @ApiModelProperty(value = "赠送他人信息")
        private List<ComputedOrderCommonPriceRequest.UserInformation> giveAwayToOthersMap;
    }

    @Data
    public static class UserInformation {
        private String appointmentOfClassStaff;
        private String appointmentPhoneNumber;

        public void setAppointmentOfClassStaff(String appointmentOfClassStaff) {
            if (StringUtils.isBlank(appointmentOfClassStaff)){
                throw new VcaException("预约人姓名不能为空");
            }
            this.appointmentOfClassStaff = appointmentOfClassStaff;
        }

        public void setAppointmentPhoneNumber(String appointmentPhoneNumber) {
            if (StringUtils.isBlank(appointmentPhoneNumber)){
                throw new VcaException("预约人手机号不能为空");
            }
            this.appointmentPhoneNumber = appointmentPhoneNumber;
        }
    }


}
