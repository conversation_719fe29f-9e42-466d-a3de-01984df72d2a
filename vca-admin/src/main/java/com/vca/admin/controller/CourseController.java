package com.vca.admin.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import com.vca.admin.config.WebConfig;
import com.vca.admin.config.WxMaConfiguration;
import com.vca.common.page.CommonPage;
import com.vca.common.request.CourseAddRequest;
import com.vca.common.request.PageParamRequest;
import com.vca.common.request.AdminCommonRequest;
import com.vca.common.request.SelectCourseRequest;
import com.vca.common.response.AdminHeaderResponse;
import com.vca.common.response.CourseAdminListResponse;
import com.vca.common.response.CourseAdminResponse;
import com.vca.common.response.SelectCourseResponse;
import com.vca.common.result.CommonResult;
import com.vca.service.service.CourseService;
import com.vca.service.service.SystemGroupDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程控制器
 *
 * <AUTHOR>
 * @date 2022/10/18   13:10
 */
@Slf4j
@RestController("CourseController")
@RequestMapping("api/admin/course")
@Api(tags = "课程控制器")
public class CourseController {
    @Autowired
    private CourseService courseService;

    @Autowired
    private SystemGroupDataService systemGroupDataService;

    @Autowired
    private WebConfig webConfig;

    /**
     * @Description:新增课程
     * @Author: chenBing
     * @Date: 2022/11/7
     */@PreAuthorize("hasAuthority('admin:course:add')")
    @ApiOperation(value = "新增课程")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public CommonResult<String> addCourse(@RequestBody CourseAddRequest request) {
        if (courseService.addCourse(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 获得课程后台配置
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/11 10:39
     */
    @ApiOperation("课程配置")
    @GetMapping("/getCourseAdminConfig")
    public CommonResult getCourseAdminConfig() {
        return CommonResult.success(systemGroupDataService.getCourseAdminConfig());
    }


    /**
     * 课程管理
     *
     * @param request
     * @param pageParamRequest
     * @return
     * <AUTHOR>
     * @date 2022/11/11 10:00
     */
    @PreAuthorize("hasAuthority('admin:course:list')")
    @ApiOperation("课程管理")
    @GetMapping("/list")
    public CommonResult<CommonPage<CourseAdminListResponse>> getList(@ModelAttribute @Validated AdminCommonRequest request, @ModelAttribute PageParamRequest pageParamRequest) {
        CommonPage commonPage = CommonPage.restPage(courseService.getAdminList(request, pageParamRequest));
        return CommonResult.success(commonPage);
    }

    /**
     * 课程排序清零
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    @PreAuthorize("hasAuthority('admin:course:sort:clear')")
    @ApiOperation("课程排序清零")
    @GetMapping("/SortClearZero")
    public CommonResult getSortClearZero() {
        return courseService.getSortClearZero() ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 修改排序
     *
     * @param courseId 课程id
     * @param sort     排序
     * @return
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    @PreAuthorize("hasAuthority('admin:course:update:sort')")
    @ApiOperation("修改排序")
    @GetMapping("/updateSort")
    public CommonResult updateSort(Integer courseId, Integer sort) {
        return courseService.updateSort(courseId, sort) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 修改席位
     *
     * @param courseId  课程id
     * @param seatCount 席位数量
     * @return
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    @PreAuthorize("hasAuthority('admin:course:update:count')")
    @ApiOperation("修改席位")
    @GetMapping("/updateSeatCount")
    public CommonResult updateSeatCount(Integer courseId, Integer seatCount) {
        return courseService.updateSeatCount(courseId, seatCount) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 上架课程
     *
     * @param courseId 课程id
     * @return
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    @PreAuthorize("hasAuthority('admin:course:shell:on')")
    @ApiOperation("上架课程")
    @GetMapping("/courseOnShell")
    public CommonResult courseOnShell(Integer courseId) {
        return courseService.courseOnShell(courseId) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 下架课程
     *
     * @param courseId 课程id
     * @return
     * <AUTHOR>
     * @date 2022/11/17 09:47
     */
    @PreAuthorize("hasAuthority('admin:course:shell:off')")
    @ApiOperation("下架课程")
    @GetMapping("/courseOffShell")
    public CommonResult courseOffShell(Integer courseId) {
        return courseService.courseOffShell(courseId) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 根据id获取课程信息
     *
     * @param courseId
     * @return
     * <AUTHOR>
     * @date 2022/11/22 10:25
     */
    @PreAuthorize("hasAuthority('admin:course:get')")
    @ApiOperation("根据id获取课程信息")
    @GetMapping("/getCourse")
    public CommonResult<CourseAdminResponse> getCourse(Long courseId) {
        return CommonResult.success(courseService.getCourse(courseId));
    }

    /**
     * @Description:修改课程
     * @Author: Li
     * @Date: 2022/11/22 14:56
     */
    @PreAuthorize("hasAuthority('admin:course:update')")
    @ApiOperation(value = "修改课程")
    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody CourseAddRequest request) {
        return courseService.updateCourse(request) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 获得课程头部数据
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/23 09:16
     */
    @ApiOperation(value = "获得课程头部数据")
    @GetMapping("/header")
    public CommonResult<List<AdminHeaderResponse>> getHeader() {
        return CommonResult.success(courseService.getHeader());
    }

    /**
     * 删除课程
     *
     * @return
     * <AUTHOR>
     * @date 2022/11/23 09:16
     */
    @PreAuthorize("hasAuthority('admin:course:delete')")
    @ApiOperation(value = "删除课程")
    @GetMapping("/delete")
    public CommonResult delete(Long courseId) {
        return courseService.deleteCourse(courseId) ? CommonResult.success() : CommonResult.failed();
    }

    /**
     * 生成合成图片
     */
    @ApiOperation(value = "生成合成图片")
    @GetMapping("/generateImage/{courseId}")
    public CommonResult generateImage(@PathVariable Long courseId) {

        Map<String,Object> map = courseService.generateImage(courseId);

        final WxMaService wxService = WxMaConfiguration.getMaService("wxbd3b07b6a1ece617");
        String miniprogramState = webConfig.getEnv().equals("prod") ? "release" : WxMaConstants.MiniProgramState.TRIAL;

        try {
            byte[] file = wxService.getQrcodeService().createWxaCodeUnlimitBytes("id="+courseId, "pages/course-detail/index", true, miniprogramState, 430, true, (WxMaCodeLineColor) null, true);
            map.put("file", file);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }

        return CommonResult.success(map);
    }

    /**
     * 选择课程
     *
     * @param request 请求参数
     * @return 课程选择列表
     * <AUTHOR>
     * @date 2023-06-25
     */
    @ApiOperation(value = "选择课程")
    @PreAuthorize("hasAuthority('admin:course:list')")
    @PostMapping("/select")
    public CommonResult<List<SelectCourseResponse>> selectCourse(@RequestBody SelectCourseRequest request) {
        return CommonResult.success(courseService.getSelectCourseList(request));
    }
}
