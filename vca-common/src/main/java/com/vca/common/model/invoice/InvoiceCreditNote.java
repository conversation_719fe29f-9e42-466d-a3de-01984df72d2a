package com.vca.common.model.invoice;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 发票红冲表
 * @date 2023-02-08 15:54
 */

@Data
@TableName("vca_invoice_credit_note")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="InvoiceRecord对象", description="发票红冲表")
public class InvoiceCreditNote implements Serializable {

    private static final long serialVersionUID = -1817996481723434425L;

    @ApiModelProperty(value = "商品明细ID 必须保证同一订单下该属性值唯一")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "子订单红冲金额")
    private BigDecimal price;

    @ApiModelProperty(value = "红冲发票url")
    private String url;
}
