package com.vca.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 选择课程请求对象
 * <AUTHOR>
 * @date 2023-06-25
 */
@Data
@ApiModel(value = "选择课程请求对象")
public class SelectCourseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程名称关键词")
    private String keyword;
} 