package com.vca.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CoursePackageCancelVo
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2024/2/21 17:45
 **/
@Data
public class CoursePackageCancelVo {

    private String orderNo;


    private List<OrderCancelVo> orderCancelVoList;


    @Data
    public static class OrderCancelVo {
        private String merOrderNo;
        private BigDecimal price;
    }
}