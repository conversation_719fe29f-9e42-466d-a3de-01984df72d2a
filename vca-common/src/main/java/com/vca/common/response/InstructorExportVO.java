package com.vca.common.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 讲师导出VO类
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@HeadRowHeight(25)
@ContentRowHeight(20)
public class InstructorExportVO {

    @ExcelProperty(value = "讲师ID")
    @ColumnWidth(10)
    private Long id;

    @ExcelProperty(value = "讲师姓名")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty(value = "邮箱")
    @ColumnWidth(30)
    private String email;

    @ExcelProperty(value = "手机国际区号")
    @ColumnWidth(15)
    private String phoneCode;

    @ExcelProperty(value = "手机号码")
    @ColumnWidth(20)
    private String phoneNumber;

    @ExcelProperty(value = "讲师类型")
    @ColumnWidth(40)
    private String types;

    @ExcelProperty(value = "课程")
    @ColumnWidth(50)
    private String courses;
} 