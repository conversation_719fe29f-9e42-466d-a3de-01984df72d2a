package com.vca.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vca.common.model.instructor.InstructorTypeRelation;
import com.vca.common.response.InstructorTypeResponse;

import java.util.List;

/**
 * <p>
 * 讲师与类型关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface InstructorTypeRelationService extends IService<InstructorTypeRelation> {
    /**
     * 保存讲师与类型关联
     * @param instructorId
     * @param typeIds
     */
    void saveInstructorTypeRelations(Long instructorId, List<Integer> typeIds);

    /**
     * 更新讲师与类型关联
     * @param instructorId
     * @param typeIds
     */
    void updateInstructorTypeRelations(Long instructorId, List<Integer> typeIds);

    /**
     * 根据讲师ID删除关联记录
     * @param instructorId
     */
    void removeByInstructorId(Long instructorId);

}
