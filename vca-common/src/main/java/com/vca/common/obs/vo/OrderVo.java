//package com.vca.common.obs.vo;
//
//import com.alibaba.excel.annotation.ExcelProperty;
//import com.alibaba.excel.annotation.write.style.ContentRowHeight;
//import com.alibaba.excel.annotation.write.style.HeadRowHeight;
//import com.alibaba.excel.annotation.write.style.OnceAbsoluteMerge;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.math.BigDecimal;
//import java.util.Date;
//
//@Data
//@EqualsAndHashCode
//@ContentRowHeight(20)
//@HeadRowHeight(25)
//@OnceAbsoluteMerge(firstRowIndex = 0, lastRowIndex = 0, firstColumnIndex = 0, lastColumnIndex = 73)
//public class OrderVo {
//
//    @ExcelProperty(value = "订单ID")
//    private Integer id;
//
//    @ExcelProperty(value = "订单号")
//    private String orderId;
//
//    @ExcelProperty(value = "用户id")
//    private Integer uid;
//
//    @ExcelProperty(value = "用户姓名")
//    private String realName;
//
//    @ExcelProperty(value = "用户电话")
//    private String userPhone;
//
//    @ExcelProperty(value = "详细地址")
//    private String userAddress;
//
//    @ExcelProperty(value = "收货人所在省")
//    private String province;
//
//    @ExcelProperty(value = "收货人所在市")
//    private String city;
//
//    @ExcelProperty(value = "收货人所在区")
//    private String district;
//
//    @ExcelProperty(value = "收货人详细地址")
//    private String detail;
//
//    @ExcelProperty(value = "运费金额")
//    private BigDecimal freightPrice;
//
//    @ExcelProperty(value = "订单商品总数")
//    private Integer totalNum;
//
//    @ExcelProperty(value = "订单总价")
//    private BigDecimal totalPrice;
//
//    @ExcelProperty(value = "邮费")
//    private BigDecimal totalPostage;
//
//    @ExcelProperty(value = "实际支付金额")
//    private BigDecimal payPrice;
//
//    @ExcelProperty(value = "购买积分商品所花费的积分")
//    private Integer payIntegral;
//
//    @ExcelProperty(value = "积分商品ID   0=没有积分商品")
//    private Integer integralId;
//
//    @ExcelProperty(value = "支付邮费")
//    private BigDecimal payPostage;
//
//    @ExcelProperty(value = "抵扣金额（积分）")
//    private BigDecimal deductionPrice;
//
//    @ExcelProperty(value = "礼品卡id")
//    private Integer couponId;
//
//    @ExcelProperty(value = "礼品卡金额（面值）")
//    private BigDecimal couponPrice;
//
//    @ExcelProperty(value = "支付状态 0未支付 1已支付")
//    private Integer paid;
//
//    @ExcelProperty(value = "支付方式")
//    private String payType;
//
//    @ExcelProperty(value = "支付时间")
//    private Date payTime;
//
//    @ExcelProperty(value = "创建时间")
//    private Date createTime;
//
//    @ExcelProperty(value = "订单状态（0：待发货；1：待收货【已预约（未参与）】；2：已收货/已完成，待评价【已参与】；3：已完成（已评价）；）")
//    private Boolean status;
//
//    @ExcelProperty(value = "退货订单号")
//    private String returnOrderNo;
//
//    @ExcelProperty(value = "退货运单号")
//    private String returnWaybillNo;
//
//    @ExcelProperty(value = "退货人姓名")
//    private String returnUserName;
//
//    @ExcelProperty(value = "退货人手机号")
//    private String returnUserPhone;
//
//    @ExcelProperty(value = "退货人地址")
//    private String returnUserAddress;
//
//    @ExcelProperty(value = "同意退货时间")
//    private Date agreedReturnTime;
//
//    @ExcelProperty(value = "取件日期")
//    private String pickUpDate;
//
//    @ExcelProperty(value = "取件开始时间")
//    private String pickUpStartTime;
//
//    @ExcelProperty(value = "取件结束时间")
//    private String pickUpEndTime;
//
//    @ExcelProperty(value = "退款状态  0 未退款 1 申请中 2 已退款 3 退款中  4 待退货 5 退货中  6退款取消")
//    private Integer refundStatus;
//
//    @ExcelProperty(value = "退款图片")
//    private String refundReasonWapImg;
//
//    @ExcelProperty(value = "退款用户说明")
//    private String refundReasonWapExplain;
//
//    @ExcelProperty(value = "前台退款原因")
//    private String refundReasonWap;
//
//    @ExcelProperty(value = "不退款的理由")
//    private String refundReason;
//
//    @ExcelProperty(value = "退款申请时间")
//    private Date refundApplicationTime;
//
//    @ExcelProperty(value = "退款时间")
//    private Date refundReasonTime;
//
//    @ExcelProperty(value = "退款金额")
//    private BigDecimal refundPrice;
//
//    @ExcelProperty(value = "快递名称/送货人姓名")
//    private String deliveryName;
//
//    @ExcelProperty(value = "发货类型")
//    private String deliveryType;
//
//    @ExcelProperty(value = "发货时间")
//    private Date deliveryTime;
//
//    @ExcelProperty(value = "退货发货时间")
//    private Date returnDeliveryTime;
//
//    @ExcelProperty(value = "主运单号（快递运单号） ")
//    private String masterWaybillNo;
//
//    @ExcelProperty(value = "消费赚取积分")
//    private Integer gainIntegral;
//
//    @ExcelProperty(value = "使用积分")
//    private Integer useIntegral;
//
//    @ExcelProperty(value = "给用户退了多少积分")
//    private Integer backIntegral;
//
//    @ExcelProperty(value = "备注")
//    private String mark;
//
//    @ExcelProperty(value = "是否删除（用户取消/系统过期取消）")
//    private Integer isDel;
//
//    @ExcelProperty(value = "管理员备注")
//    private String remark;
//
//    @ExcelProperty(value = "成本价")
//    private BigDecimal cost;
//
//    @ExcelProperty(value = "核销码")
//    private String verifyCode;
//
//    @ExcelProperty(value = "店员id/核销员id")
//    private Integer clerkId;
//
//    @ExcelProperty(value = "支付渠道(0微信公众号1微信小程序2余额)")
//    private Integer isChannel;
//
//    @ExcelProperty(value = "后台是否删除（系统过期取消）")
//    private Boolean isSystemDel;
//
//    @ExcelProperty(value = "更新时间")
//    private Date updateTime;
//
//    @ExcelProperty(value = "快递公司简称")
//    private String deliveryCode;
//
//    @ExcelProperty(value = "订单类型:0-课程订单 1=套课订单 2=讲座订单 3=展览订单 4=商品订单")
//    private Integer type;
//
//    @ExcelProperty(value = "商品总价")
//    private BigDecimal proTotalPrice;
//
//    @ExcelProperty(value = "改价前支付金额")
//    private BigDecimal beforePayPrice;
//
//    @ExcelProperty(value = "是否改价,0-否，1-是")
//    private Boolean isAlterPrice;
//
//    @ExcelProperty(value = "发货顺丰状态：0:顺丰下单失败 1 顺丰下单成功  2 顺丰订单取消成功 3顺丰取消失败 4 顺丰订单拦截成功 5 顺丰订单拦截失败")
//    private Boolean deliverySfStatus;
//
//    @ExcelProperty(value = "退货顺丰状态：0:顺丰下单失败 1 顺丰下单成功  2 顺丰订单取消成功 3顺丰取消失败 4 顺丰订单拦截成功 5 顺丰订单拦截失败")
//    private Boolean returnSfStatus;
//
//    @ExcelProperty(value = "银联全民付 交易号")
//    private String umsMerOrderId;
//
//    @ExcelProperty(value = "银联全民付 微信交易号")
//    private String targetOrderId;
//
//    @ExcelProperty(value = "开票状态  0=未开票 1=已开票")
//    private Integer invoicingStatus;
//
//    @ExcelProperty(value = "0=用户购买  1=管理员赠送")
//    private Integer userType;
//
//    @ExcelProperty(value = "申请退款类型")
//    private String applyRefundType;
//
//    @ExcelProperty(value = "成交时间")
//    private Date closingTime;
//
//    @ExcelProperty(value = "售后截止时间")
//    private Date afterSalesDeadline;
//}
