package com.vca.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vca.common.model.instructor.Instructor;
import com.vca.common.page.CommonPage;
import com.vca.common.request.InstructorListRequest;
import com.vca.common.request.InstructorRequest;
import com.vca.common.request.PageParamRequest;
import com.vca.common.response.InstructorResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 讲师主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface InstructorService extends IService<Instructor> {

    /**
     * 讲师分页列表
     * @param param 参数
     * @return 分页对象
     */
    CommonPage<InstructorResponse> list(InstructorListRequest param);

    /**
     * 新增讲师
     * @param request 讲师请求对象
     */
    void addInstructor(InstructorRequest request);

    /**
     * 编辑讲师
     * @param request 讲师请求对象
     */
    void editInstructor(InstructorRequest request);

    /**
     * 查看讲师详情
     * @param id 讲师ID
     * @return 讲师响应对象
     */
    InstructorResponse getInstructorDetail(Long id);

    /**
     * 删除讲师
     * @param id 讲师ID
     */
    void deleteInstructor(Long id);

    /**
     * 导入讲师
     * @param file Excel文件
     * @return 导入结果
     */
    String importInstructor(MultipartFile file);

    /**
     * 导出讲师
     * @param response HttpServletResponse
     * @param param
     */
    void exportInstructor(HttpServletResponse response, InstructorListRequest param);
}
