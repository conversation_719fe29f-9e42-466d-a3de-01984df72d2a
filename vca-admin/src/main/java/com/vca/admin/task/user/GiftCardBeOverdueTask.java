package com.vca.admin.task.user;

import com.vca.common.utils.DateUtil;
import com.vca.service.service.UserCardService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 *@Description:礼品卡过期提前通知
 *@author:chenbing
 *@date 2022/12/30 14:10
 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class GiftCardBeOverdueTask {

    private static final Logger logger = LoggerFactory.getLogger(GiftCardBeOverdueTask.class);

    private static final String SIXTY_MINUTE = "PT30M";
    private static final String THREE_MINUTE = "PT3M";

    @Autowired
    private UserCardService userCardService;

    @Scheduled(fixedDelay = 1000 * 60L * 60) //每小时同步一次数据
    @SchedulerLock(name = "GiftCardBeOverdueTask", lockAtMostFor = SIXTY_MINUTE, lockAtLeastFor = THREE_MINUTE)
    public void init() {
        logger.info("---GiftCardBeOverdueTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            userCardService.giftCardBeOverdue();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("GiftCardBeOverdueTask.task" + " | msg : " + e.getMessage());
        }

    }
}
