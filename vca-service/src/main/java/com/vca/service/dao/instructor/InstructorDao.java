package com.vca.service.dao.instructor;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.vca.common.model.instructor.Instructor;
import com.vca.common.request.InstructorListRequest;
import com.vca.common.response.CourseResponse;
import com.vca.common.response.InstructorResponse;
import com.vca.common.response.InstructorTypeResponse;
import com.vca.common.response.UnavailableTimeResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲师Dao接口
 * <AUTHOR>
 * @since 2025-06-16
 */
@Mapper
public interface InstructorDao extends BaseMapper<Instructor> {

    /**
     * 查询讲师分页列表
     * @param param 参数
     * @return 分页对象
     */
    Page<InstructorResponse> findInstructorPage(InstructorListRequest param);

    /**
     * 查询讲师列表（不分页）
     * @param param 参数
     * @return 讲师列表
     */
    List<InstructorResponse> findInstructorList(InstructorListRequest param);

    /**
     * 根据讲师ID列表查询讲师类型
     * @param instructorIds 讲师ID列表
     * @return 讲师类型列表
     */
    List<InstructorTypeResponse> findTypesByInstructorIds(@Param("instructorIds") List<Long> instructorIds);

    /**
     * 根据讲师ID列表查询课程
     * @param instructorIds 讲师ID列表
     * @return 课程列表
     */
    List<CourseResponse> findCoursesByInstructorIds(@Param("instructorIds") List<Long> instructorIds);

    /**
     * 根据讲师ID列表查询不可授课时间
     * @param instructorIds 讲师ID列表
     * @return 不可授课时间列表
     */
    List<UnavailableTimeResponse> findUnavailableTimesByInstructorIds(@Param("instructorIds") List<Long> instructorIds);
}
