package com.vca.common.model.sf;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WaybillNoInfo对象", description="顺丰运单号信息")
public class WaybillNoInfo implements Serializable {

    private static final long serialVersionUID = -2569881900192950637L;

    @ApiModelProperty(value = "运单号类型 1：母单 2 :子单 3 : 签回单")
    private Integer waybillType;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "箱号")
    private String boxNo;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;

    @ApiModelProperty(value = "体积（立方厘米）")
    private String volume;
}
