package com.vca.service.service.Impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vca.common.model.order.StoreOrderInfo;
import com.vca.common.vo.OrderGroupByAttrVo;
import com.vca.common.vo.OrderInfoDetailVo;
import com.vca.common.vo.StoreOrderInfoOldVo;
import com.vca.common.vo.StoreOrderInfoVo;
import com.vca.service.dao.order.StoreOrderInfoDao;
import com.vca.service.service.StoreOrderInfoService;
import com.vca.service.service.StoreProductReplyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.groupingBy;

/**
 * StoreOrderInfoServiceImpl 接口实现
 */
@Service
public class StoreOrderInfoServiceImpl extends ServiceImpl<StoreOrderInfoDao, StoreOrderInfo>
        implements StoreOrderInfoService {

    @Resource
    private StoreOrderInfoDao dao;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    /**
     * 根据id集合查询数据，返回 map
     * @param orderList List<Integer> id集合
     * <AUTHOR>
     * @since 2020-04-17
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public HashMap<String, List<StoreOrderInfoOldVo>> getMapInId(List<String> orderList){
        HashMap<String, List<StoreOrderInfoOldVo>> map = new HashMap<>();
        if(orderList.size() < 1){
            return map;
        }
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(StoreOrderInfo::getOrderNo, orderList);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if(systemStoreStaffList.size() < 1){
            return map;
        }
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            //解析商品详情JSON
            StoreOrderInfoOldVo StoreOrderInfoVo = new StoreOrderInfoOldVo();
            BeanUtils.copyProperties(storeOrderInfo, StoreOrderInfoVo, "info");
            StoreOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            if(map.containsKey(storeOrderInfo.getOrderNo())){
                map.get(storeOrderInfo.getOrderNo()).add(StoreOrderInfoVo);
            }else{
                List<StoreOrderInfoOldVo> storeOrderInfoVoList = new ArrayList<>();
                storeOrderInfoVoList.add(StoreOrderInfoVo);
                map.put(storeOrderInfo.getOrderNo(), storeOrderInfoVoList);
            }
        }
        return map;
    }


    public Map<String,List<StoreOrderInfo>> getMapInIds(List<String> orderList){

        if(orderList.size() < 1){
            return new HashMap<>();
        }
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(StoreOrderInfo::getOrderNo, orderList);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);

        Map<String, List<StoreOrderInfo>> hashMap = systemStoreStaffList.stream().collect(groupingBy(StoreOrderInfo::getOrderNo));
        return hashMap;
    }

    /**
     * 根据id集合查询数据，返回 map
     * @param orderId Integer id
     * <AUTHOR>
     * @since 2020-04-17
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfo> getOrderListByOrderId(String orderId){
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderNo, orderId);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if(systemStoreStaffList.size() < 1){
            return null;
        }

        List<StoreOrderInfo> storeOrderInfoVoList = new ArrayList<>();
//        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
//            //解析商品详情JSON
//            StoreOrderInfo storeOrderInfo = new StoreOrderInfo();
//            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfo, "info");
//            storeOrderInfo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), storeOrderInfo.class));
//            storeOrderInfo.getInfo().setIsReply(
//                    storeProductReplyService.isReply(storeOrderInfo.getUnique(), storeOrderInfo.getOrderId()) ? 1 : 0
//            );
//            storeOrderInfoVoList.add(storeOrderInfoVo);
//        }
        return storeOrderInfoVoList;
    }

    /**
     * 根据id集合查询数据，返回 map
     * @param orderNo 订单No
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfoVo> getVoListByOrderId(String orderNo){
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderNo, orderNo);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if(systemStoreStaffList.size() < 1){
            return null;
        }

        List<StoreOrderInfoVo> storeOrderInfoVoList = new ArrayList<>();
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            //解析商品详情JSON
            StoreOrderInfoVo storeOrderInfoVo = new StoreOrderInfoVo();
            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfoVo, "info");
            storeOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            storeOrderInfoVoList.add(storeOrderInfoVo);
        }
        return storeOrderInfoVoList;
    }

    /**
     * 获取订单详情-订单编号
     * @param orderNo 订单编号
     * @return List
     */
    @Override
    public List<StoreOrderInfo> getListByOrderNo(String orderNo) {
        LambdaQueryWrapper<StoreOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrderInfo::getOrderNo, orderNo);
        return dao.selectList(lqw);
    }

    /**
     * 根据时间、商品id获取销售件数
     * @param date 时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return Integer
     */
    @Override
    public Integer getSalesNumByDateAndProductId(String date, Integer proId) {
        return dao.getSalesNumByDateAndProductId(date, proId);
    }

    /**
     * 根据时间、商品id获取销售额
     * @param date 时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return BigDecimal
     */
    @Override
    public BigDecimal getSalesByDateAndProductId(String date, Integer proId) {
        return dao.getSalesByDateAndProductId(date, proId);
    }

    @Override
    public List<StoreOrderInfo> refundList(String startTime, String endTime) {
        return dao.refundList(startTime,endTime);
    }

    @Override
    public List<StoreOrderInfo> couponRefundList(String startTime, String endTime) {
        return dao.couponRefundList(startTime,endTime);
    }

    @Override
    public List<StoreOrderInfo> cardRefundList(String startTime, String endTime) {
        return dao.cardRefundList(startTime,endTime);
    }


    /**
     * 新增订单详情
     * @param storeOrderInfos 订单详情集合
     * @return 订单新增结果
     */
    @Override
    public boolean saveOrderInfos(List<StoreOrderInfo> storeOrderInfos) {
        return saveBatch(storeOrderInfos);
    }

    /**
     * 通过订单编号和规格号查询
     * @param uni 规格号
     * @param orderNo 订单编号
     * @return StoreOrderInfo
     */
    @Override
    public StoreOrderInfo getByUniAndOrderId(String uni, String orderNo) {
        //带 StoreOrderInfo 类的多条件查询
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderNo, orderNo);
        return dao.selectOne(lambdaQueryWrapper);
    }
}

