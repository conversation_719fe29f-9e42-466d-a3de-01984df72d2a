package com.vca.front.controller;

import com.vca.common.result.CommonResult;
import com.vca.service.service.SystemConfigService;
import com.vca.service.service.SystemGroupDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/19   9:25
 */
@Slf4j
@RestController("HomeController")
@RequestMapping("api/front/home")
@Api(tags = "首页")
public class HomeController {

    @Autowired
    private SystemGroupDataService systemGroupDataService;

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * @Description:获取首页配置
     * @Author: chenBing
     * @Date: 2022/9/19
     */
    @ApiOperation(value = "获取首页配置")
    @RequestMapping(value = "/index/config", method = RequestMethod.GET)
    public CommonResult<HashMap<String, Object>> getIndexConfig() {
        return CommonResult.success(systemGroupDataService.getIndexConfig());
    }

    /**
    * @Description:获取课程过滤参数
    * @Author: chenBing
    * @Date: 2022/9/19
    */
    @ApiOperation(value = "获取课程过滤参数")
    @RequestMapping(value = "/course/config",method = RequestMethod.GET)
    public CommonResult<HashMap<String,Object>> getCourseConfig(@RequestHeader(value = "language",required = false) String language){
        return CommonResult.success(systemGroupDataService.getCourseConfig(language));
    }

    /**
     *@Description:系统基础配置
     *@author:chenbing
     *@date 2022/12/12 14:58
     */
    @ApiOperation(value = "系统基础配置")
    @GetMapping(value = "/base/config")
    public CommonResult<HashMap<String,Object>> getBaseConfig(){
        return CommonResult.success(systemConfigService.getBaseConfig());
    }

    /**
     * @Description:获取首页配置信息
     * @Author: Li
     * @Date: 2022/10/25 10:57
     */
    @ApiOperation(value = "获取首页配置信息")
    @GetMapping(value = "/getConfig")
    public CommonResult<HashMap<String,Object>> getConfig(@RequestHeader(value="language",required = false) String language){
        return CommonResult.success(systemGroupDataService.getConfig(language));
    }

    /**
    * @description 小程序分享
    * * @param
    * <AUTHOR>
    * @date 2023/2/14 18:05
    * @return {@link CommonResult}<{@link Map}<{@link String},{@link Object}>>
    */
    @ApiOperation(value = "获取小程序分享信息")
    @GetMapping("/getShare")
    public CommonResult<Map<String,Object>> getShare(){
        return CommonResult.success(systemConfigService.getShare());
    }

}
