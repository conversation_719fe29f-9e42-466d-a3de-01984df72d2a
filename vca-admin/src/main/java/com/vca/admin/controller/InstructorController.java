package com.vca.admin.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.vca.common.page.CommonPage;
import com.vca.common.request.InstructorListRequest;
import com.vca.common.request.InstructorRequest;
import com.vca.common.request.PageParamRequest;
import com.vca.common.response.InstructorExportVO;
import com.vca.common.response.InstructorResponse;
import com.vca.common.result.CommonResult;
import com.vca.service.service.InstructorCourseRelationService;
import com.vca.service.service.InstructorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 讲师管理控制器
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@RestController("InstructorController")
@RequestMapping("api/admin/instructor")
@Api(tags = "讲师管理控制器")
@RequiredArgsConstructor
public class InstructorController {
    private final InstructorService instructorService;
    private final InstructorCourseRelationService instructorCourseRelationService;

    /**
     * 讲师分页列表
     */
    @PreAuthorize("hasAuthority('admin:instructor:list')")
    @ApiOperation(value = "讲师分页列表")
    @PostMapping("/list")
    public CommonResult<CommonPage<InstructorResponse>> list(@RequestBody InstructorListRequest param) {
        return CommonResult.success(instructorService.list(param));
    }

    /**
     * 新增讲师
     */
    @PreAuthorize("hasAuthority('admin:instructor:add')")
    @ApiOperation(value = "新增讲师")
    @PostMapping("/add")
    public CommonResult<String> addInstructor(@RequestBody @Validated InstructorRequest request) {
        instructorService.addInstructor(request);
        return CommonResult.success();
    }

    /**
     * 编辑讲师
     */
    @PreAuthorize("hasAuthority('admin:instructor:edit')")
    @ApiOperation(value = "编辑讲师")
    @PostMapping("/edit")
    public CommonResult<String> editInstructor(@RequestBody @Validated InstructorRequest request) {
        instructorService.editInstructor(request);
        return CommonResult.success();
    }

    /**
     * 查看讲师详情
     */
    @PreAuthorize("hasAuthority('admin:instructor:detail')")
    @ApiOperation(value = "查看讲师详情")
    @GetMapping("/detail")
    public CommonResult<InstructorResponse> getInstructorDetail(@RequestParam(value = "id") Long id) {
        return CommonResult.success(instructorService.getInstructorDetail(id));
    }

    /**
     * 删除讲师和课程关联关系
     */
    @PreAuthorize("hasAuthority('admin:instructor:delete')")
    @ApiOperation(value = "删除讲师和课程关联关系")
    @PostMapping("/deleteRelation")
    public CommonResult<String> deleteInstructorCourseRelation(@RequestParam(value = "relationId") Long relationId) {
        instructorCourseRelationService.removeById(relationId);
        return CommonResult.success();
    }

    /**
     * 删除讲师
     */
    @PreAuthorize("hasAuthority('admin:instructor:delete')")
    @ApiOperation(value = "删除讲师")
    @PostMapping("/delete")
    public CommonResult<String> deleteInstructor(@RequestParam(value = "id") Long id) {
        instructorService.deleteInstructor(id);
        return CommonResult.success();
    }

    /**
     * 导入讲师
     */
    @PreAuthorize("hasAuthority('admin:instructor:import')")
    @ApiOperation(value = "导入讲师")
    @PostMapping("/import")
    public CommonResult<String> importInstructor(@RequestParam("file") MultipartFile file) {
        return CommonResult.success(instructorService.importInstructor(file));
    }

    /**
     * 导出讲师
     */
    @PreAuthorize("hasAuthority('admin:instructor:export')")
    @ApiOperation(value = "导出讲师")
    @PostMapping("/export")
    public void exportInstructor(HttpServletResponse response,@RequestBody InstructorListRequest param) throws IOException {
        instructorService.exportInstructor(response, param);
    }
}
