package com.vca.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/23   10:40
 */
@Data
@ApiModel(value = "课程推荐")
public class CourseRecommendation {
    @ApiModelProperty(value = "课程主键id")
    private Long courseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程简介")
    private String courseIntroduction;

    @ApiModelProperty(value = "课程封面图")
    private String courseCover;

    @ApiModelProperty(value = "课程类型便签颜色")
    private String courseTypeLabelColor;

    @ApiModelProperty(value = "课程类型")
    private String courseTypeName;

    @ApiModelProperty(value = "课程价格")
    private Integer coursePrice;
}
