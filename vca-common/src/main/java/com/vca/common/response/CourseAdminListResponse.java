package com.vca.common.response;

import com.baomidou.mybatisplus.annotation.*;
import com.vca.common.vo.CourseInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 课程后台管理Response
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11 13:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CourseAdminListResponse对象", description="课程后台管理Response")
public class CourseAdminListResponse implements Serializable {

    private static final long serialVersionUID = 9073129183918750982L;

    @ApiModelProperty(value = "课程主键id")
    private Long id;

    @ApiModelProperty(value = "课程名称")
    private String name;

    @ApiModelProperty(value = "英文课程名称")
    private String nameEn;

    @ApiModelProperty(value = "课程时长id")
    private Integer courseDurationId;

    @ApiModelProperty(value = "课程时长")
    private String courseDuration;

    @ApiModelProperty(value = "课程类型id")
    private Long courseTypeId;

    @ApiModelProperty(value = "课程类型")
    private String courseType;

    @ApiModelProperty(value = "课程价格id")
    private Integer coursePriceId;

    @ApiModelProperty(value = "课程价格")
    private String price;

    @ApiModelProperty(value = "课程地址id")
    private Integer courseAddressId;

    @ApiModelProperty(value = "课程地址")
    private String courseAddress;

    @ApiModelProperty(value = "课程简介")
    private String introduction;

    @ApiModelProperty(value = "英文课程简介")
    private String introductionEn;

    @ApiModelProperty(value = "席位")
    private Integer seatCount;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "课程封面图")
    private String cover;

    @ApiModelProperty(value = "课程封面图 统一字段")
    private String coverImage;

    @ApiModelProperty(value = "课程轮播图")
    private String carouselMap;

    @ApiModelProperty(value = "主图视频")
    private String mainPictureVideo;

    @ApiModelProperty(value = "分享海报")
    private String sharePoster;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty("比例")
    private Double ratio;

    @ApiModelProperty(value = "是否加入回收站")
    private Boolean isRecycle;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty("课程详情")
    private CourseInfoVo courseInfo;

}
