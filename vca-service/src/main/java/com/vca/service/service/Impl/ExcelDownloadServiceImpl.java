package com.vca.service.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.vca.common.excel.ExcelDownload;
import com.vca.common.page.CommonPage;
import com.vca.common.request.PageParamRequest;
import com.vca.service.dao.excel.ExcelDownloadDao;
import com.vca.service.service.ExcelDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName ExcelDownloadServiceImpl
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2025/1/2 18:05
 **/
@Service
public class ExcelDownloadServiceImpl extends ServiceImpl<ExcelDownloadDao, ExcelDownload> implements ExcelDownloadService {

    @Autowired
    private ExcelDownloadDao excelDownloadDao;

    @Override
    public CommonPage getList(PageParamRequest pageParamRequest) {
        LambdaQueryWrapper<ExcelDownload> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ExcelDownload::getCreateTime);
        PageInfo pageInfo = new PageInfo(excelDownloadDao.selectList(queryWrapper));

        return CommonPage.restPage(pageInfo);
    }
}
