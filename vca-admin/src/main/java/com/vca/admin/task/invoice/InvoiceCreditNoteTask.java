package com.vca.admin.task.invoice;

import com.vca.admin.task.order.AdminOrderCancelTask;
import com.vca.common.utils.DateUtil;
import com.vca.service.service.InvoiceRecordService;
import com.vca.service.service.OrderTaskService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 红冲发票定时任务
 * @date 2023-02-15 10:32
 */

@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class InvoiceCreditNoteTask {

    //日志
    private static final Logger logger = LoggerFactory.getLogger(AdminOrderCancelTask.class);

    private static final String SIXTY_MINUTE = "PT1M";
    private static final String THREE_MINUTE = "PT1M";

    @Autowired
    private OrderTaskService orderTaskService;

    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    @SchedulerLock(name = "InvoiceCreditNoteTask", lockAtMostFor = SIXTY_MINUTE, lockAtLeastFor = THREE_MINUTE)
    public void init() {
        logger.info("---InvoiceCreditNoteTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            orderTaskService.creditNote();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("InvoiceCreditNoteTask.task" + " | msg : " + e.getMessage());
        }

    }
}
