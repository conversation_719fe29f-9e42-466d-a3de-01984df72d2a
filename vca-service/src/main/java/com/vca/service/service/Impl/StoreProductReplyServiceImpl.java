package com.vca.service.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vca.common.constants.Constants;
import com.vca.common.exception.VcaException;
import com.vca.common.model.order.StoreOrder;
import com.vca.common.model.order.StoreOrderInfo;
import com.vca.common.model.product.StoreProductReply;
import com.vca.common.model.user.User;
import com.vca.common.page.CommonPage;
import com.vca.common.request.PageParamRequest;
import com.vca.common.request.StoreProductReplyAddRequest;
import com.vca.common.request.StoreProductReplyCommentRequest;
import com.vca.common.request.StoreProductReplySearchRequest;
import com.vca.common.response.StoreProductReplyResponse;
import com.vca.common.utils.DateUtil;
import com.vca.common.utils.PageUtils;
import com.vca.common.utils.RedisUtil;
import com.vca.common.vo.PreOrderCommonVo;
import com.vca.common.vo.dateLimitUtilVo;
import com.vca.service.dao.order.StoreOrderInfoDao;
import com.vca.service.dao.product.StoreProductReplyDao;
import com.vca.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * StoreProductReplyServiceImpl 接口实现
 */
@Service
public class StoreProductReplyServiceImpl extends ServiceImpl<StoreProductReplyDao, StoreProductReply>
        implements StoreProductReplyService {

    @Resource
    private StoreProductReplyDao dao;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Resource
    private StoreOrderInfoDao storeOrderInfoDao;


    /**
     * 列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页类参数
     * @return List<StoreProductReply>
     */
    @Override
    public PageInfo<StoreProductReplyResponse> getList(StoreProductReplySearchRequest request, PageParamRequest pageParamRequest) {
        List<StoreProductReplyResponse> dataResList = new ArrayList<>();
        //带 StoreProductReply 类的多条件查询
        LambdaQueryWrapper<StoreProductReply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreProductReply::getIsDel, false);
        //是否回复
        if (ObjectUtil.isNotNull(request.getIsReply())) {
            lambdaQueryWrapper.eq(StoreProductReply::getIsReply, request.getIsReply());
        }
        //订单编号
        if (StrUtil.isNotBlank(request.getOrderNo())) {
            lambdaQueryWrapper.like(StoreProductReply::getOrderNo, request.getOrderNo());
        }
        //订单详情
//        if (StrUtil.isNotBlank(request.getProductSearch())) {
////            List<StoreProduct> storeProducts = storeProductService.likeProductName(request.getProductSearch());
//            LambdaQueryWrapper<StoreOrderInfo> storeOrderInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            storeOrderInfoLambdaQueryWrapper.like(StoreOrderInfo::getName, request.getProductSearch());
//            storeOrderInfoLambdaQueryWrapper.eq(StoreOrderInfo::getIsGet, 1);
//            storeOrderInfoLambdaQueryWrapper.eq(StoreOrderInfo::getStatus, 3);
//            List<StoreOrderInfo> list = storeOrderInfoService.list(storeOrderInfoLambdaQueryWrapper);
//            if (CollUtil.isNotEmpty(list)) {
//                List<String> orderIds = list.stream().map(StoreOrderInfo::getOrderNo).collect(Collectors.toList());
//                lambdaQueryWrapper.in(StoreProductReply::getOrderNo, orderIds);
//            }else {
//                return CommonPage.copyPageInfo(pageStoreReply, dataResList);
//            }
//        }
        if (StrUtil.isNotBlank(request.getProductSearch())) {
            lambdaQueryWrapper.like(StoreProductReply::getInfo, request.getProductSearch());
        }
        //名字、手机、邮箱 模糊查询
        if (StringUtils.isNotBlank(request.getNickname())) {
            LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.and(wrapper -> {
                wrapper.like(User::getNickname, request.getNickname()).or().like(User::getPhone, request.getNickname()).or().like(User::getEmail, request.getNickname());
            });
            List<User> userList = userService.list(userLambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(userList)) {
                List<Integer> uIdList = userList.stream().map(User::getUid).collect(Collectors.toList());
                lambdaQueryWrapper.in(StoreProductReply::getUid, uIdList);
            }else {
                lambdaQueryWrapper.eq(StoreProductReply::getId, -1);
            }
        }
        //时间
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(StoreProductReply::getCreateTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        //类型
        if (request.getType().equals(5)) {
            lambdaQueryWrapper.between(StoreProductReply::getReplyType, 0, 4);
        } else {
            lambdaQueryWrapper.eq(StoreProductReply::getReplyType, request.getType());
        }
        //预约人姓名 或 手机号
        //预约时间区间
        if (StringUtils.isNotBlank(request.getLecturerPhone()) || StringUtils.isNotBlank(request.getSchedulingDate())) {
            String lecturerPhone = request.getLecturerPhone();
            String schedulingDate = request.getSchedulingDate();
            LambdaQueryWrapper<StoreOrderInfo> storeOrderInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if(StringUtils.isNotBlank(lecturerPhone)) {
                storeOrderInfoLambdaQueryWrapper.and(wrapper -> {
                    wrapper.like(StoreOrderInfo::getLecturerName, lecturerPhone).or().like(StoreOrderInfo::getLecturerPhone, lecturerPhone);
                });
            }
            if(StringUtils.isNotBlank(schedulingDate)) {
                String[] split = schedulingDate.split(",");
                storeOrderInfoLambdaQueryWrapper.ge(StoreOrderInfo::getSchedulingDate, split[0]);
                storeOrderInfoLambdaQueryWrapper.le(StoreOrderInfo::getSchedulingDate, split[1]);
            }
            List<StoreOrderInfo> storeOrderInfos = storeOrderInfoService.list(storeOrderInfoLambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(storeOrderInfos)) {
                List<String> orderNoList = storeOrderInfos.stream().map(StoreOrderInfo::getOrderNo).collect(Collectors.toList());
                lambdaQueryWrapper.in(StoreProductReply::getOrderNo, orderNoList);
            }else {
                lambdaQueryWrapper.eq(StoreProductReply::getId, -1);
            }

        }
        lambdaQueryWrapper.orderByDesc(StoreProductReply::getId);
        //评价列表
        List<StoreProductReply> dataList = dao.selectList(lambdaQueryWrapper);

        //订单号list
        List<String> orderList = dataList.stream().map(StoreProductReply::getOrderNo).collect(Collectors.toList());

        //根据订单号分组获取订单详情map
        Map<String, List<StoreOrderInfo>> map = storeOrderInfoService.getMapInIds(orderList);


        //订单id集合
//        List<String> orderIdList = orderList.stream().map(StoreOrder::getOrderId).distinct().collect(Collectors.toList());


        for (StoreProductReply productReply : dataList) {
            StoreProductReplyResponse productReplyResponse = new StoreProductReplyResponse();
            BeanUtils.copyProperties(productReply, productReplyResponse);
            List<PreOrderCommonVo.PreOrderProductDetail> preOrderProductDetail = new ArrayList<>();
            for (StoreOrderInfo storeOrderInfo : map.get(productReply.getOrderNo())) {
                //商品订单特殊处理
                if (productReply.getReplyType().equals(4)) {
                    preOrderProductDetail.add(JSONObject.parseObject(storeOrderInfo.getInfo(), PreOrderCommonVo.PreOrderProductDetail.class));
                    productReplyResponse.setPreOrderProductDetails(preOrderProductDetail);
                } else {
                    productReplyResponse.setCourseInfoDetails(JSONObject.parseObject(storeOrderInfo.getInfo(), PreOrderCommonVo.CourseInfoDetail.class));
                }
            }
                // 添加订单类型信息
                String orderTypeFormat = "[{}订单]{}";
                String orderType = "";
//        // 核销
//        if (StrUtil.isNotBlank(storeOrder.getVerifyCode())) {
//            orderType = StrUtil.format(orderTypeFormat, "核销", "");
                if (productReply.getReplyType().equals(0)) {
                    orderType = StrUtil.format(orderTypeFormat, "课程", "");
                }
                if (productReply.getReplyType().equals(1)) {
                    orderType = StrUtil.format(orderTypeFormat, "套课", "");
                }
                if (productReply.getReplyType().equals(2)) {
                    orderType = StrUtil.format(orderTypeFormat, "讲座", "");
                }
                if (productReply.getReplyType().equals(3)) {
                    orderType = StrUtil.format(orderTypeFormat, "展览", "");
                }
                if (productReply.getReplyType().equals(4)) {
                    orderType = StrUtil.format(orderTypeFormat, "商品", "");
                }
                productReplyResponse.setOrderType(orderType);
//                productReplyResponse.setPics(VcaUtil.stringToArrayStr(productReply.getPics()));
                dataResList.add(productReplyResponse);
            }
//            Page<StoreProductReply> pageStoreReply = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
//            PageInfo<StoreProductReplyResponse> returnPage = CommonPage.copyPageInfo(pageStoreReply, dataResList);
            return PageUtils.startPage(dataResList, pageParamRequest.getPage(), pageParamRequest.getLimit());
        }

        /**
         * 商品分数
         *
         * @return Integer
         */
//        private Integer getSumStar (Integer productId){
//            QueryWrapper<StoreProductReply> queryWrapper = new QueryWrapper<>();
//            queryWrapper.select("IFNULL(sum(product_score),0) as product_score", "IFNULL(sum(service_score),0) as service_score");
//            queryWrapper.eq("is_del", 0);
//            queryWrapper.eq("product_id", productId);
//            StoreProductReply storeProductReply = dao.selectOne(queryWrapper);
//            if (ObjectUtil.isNull(storeProductReply)) {
//                return 0;
//            }
//            if (storeProductReply.getProductScore() == 0 || storeProductReply.getServiceScore() == 0) {
//                return 0;
//            }
//            // 星数 = （商品评星 + 服务评星） / 2
//            BigDecimal sumScore = new BigDecimal(storeProductReply.getProductScore() + storeProductReply.getServiceScore());
//            BigDecimal divide = sumScore.divide(BigDecimal.valueOf(2L), 0, BigDecimal.ROUND_DOWN);
//            return divide.intValue();
//        }

        /**
         * 创建订单商品评价
         *
         * @param request 请求参数
         * @return Boolean
         */
        @Override
        public Boolean create (StoreProductReplyAddRequest request){

            StoreProductReply storeProductReply = new StoreProductReply();
            User user = userService.getInfoException();
            StoreOrder storeOrder = storeOrderService.getByOrderId(request.getOrderNo());
            if (ObjectUtil.isNull(storeOrder) || !storeOrder.getUid().equals(user.getUid())) {
                throw new VcaException("该订单不存在");
            }

            List<StoreOrderInfo> storeOrderInfoList = storeOrderInfoService.getListByOrderNo(request.getOrderNo());
            for (StoreOrderInfo storeOrderInfo : storeOrderInfoList) {
                storeProductReply.setInfo(storeOrderInfo.getInfo());
                storeOrderInfo.setStatus(3);
            }


            BeanUtils.copyProperties(request, storeProductReply);
            storeProductReply.setOid(storeOrder.getId());
//        Integer count = checkIsReply(storeProductReply);
            storeProductReply.setUid(user.getUid());
            storeProductReply.setReplyType(storeOrder.getType());
            storeProductReply.setMainId(storeOrderInfoList.get(0).getMainId());
            storeProductReply.setAvatar(systemAttachmentService.clearPrefix(user.getAvatar()));
            storeProductReply.setNickname(user.getNickname());
//            if (StringUtils.isNotBlank(request.getPics())) {
//                String pics = request.getPics().replace("[\"", "").replace("\"]", "")
//                        .replace("\"", "");
//                storeProductReply.setPics(systemAttachmentService.clearPrefix(ArrayUtils.toString(pics)));
//            }
            Boolean execute = transactionTemplate.execute(e -> {
                save(storeProductReply);
                storeOrder.setStatus(Constants.ORDER_STATUS_INT_COMPLETE);
                storeOrderInfoService.updateBatchById(storeOrderInfoList);
                storeOrderService.updateById(storeOrder);
                //修改订单信息
//                completeOrder(storeProductReply, 1, storeOrder, storeOrderInfoList);
                return Boolean.TRUE;
            });
            if (!execute) {
                throw new VcaException("评价订单失败");
            }
            return execute;
        }

        /**
         * 添加虚拟评论
         *
         * @param request 评论参数
         * @return 评论结果
         */
        @Override
        public boolean virtualCreate (StoreProductReplyAddRequest request){
            StoreProductReply storeProductReply = new StoreProductReply();
            BeanUtils.copyProperties(request, storeProductReply);
//            if (StringUtils.isNotBlank(request.getPics())) {
//                String pics = request.getPics()
//                        .replace("[", "")
//                        .replace("]", "")
//                        .replace("\"", "");
//                storeProductReply.setPics(systemAttachmentService.clearPrefix(ArrayUtils.toString(pics)));
//            }
            storeProductReply.setAvatar(systemAttachmentService.clearPrefix(storeProductReply.getAvatar()));
//            storeProductReply.setUnique(VcaUtil.randomCount(11111, 9999) + "");
            return save(storeProductReply);
        }

        /**
         * 订单是否已回复
         *
         * @param unique  skuId
         * @param orderId 订单id
         * @return 回复内容
         */
        @Override
        public Boolean isReply (String unique, Integer orderId){
            LambdaQueryWrapper<StoreProductReply> lqw = new LambdaQueryWrapper<>();
//            lqw.eq(StoreProductReply::getUnique, unique);
            lqw.eq(StoreProductReply::getOid, orderId);
            List<StoreProductReply> replyList = dao.selectList(lqw);
            if (CollUtil.isEmpty(replyList)) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }

        /**
         * H5商品评论统计
         *
         * @param productId 商品编号
         * @return MyRecord
         */
//        @Override
//        public MyRecord getH5Count (Integer productId){
//            // 评论总数
//            Integer sumCount = getCountByScore(productId, "all");
//            // 好评总数
//            Integer goodCount = getCountByScore(productId, "good");
//            // 中评总数
//            Integer mediumCount = getCountByScore(productId, "medium");
//            // 差评总数
//            Integer poorCount = getCountByScore(productId, "poor");
//            // 好评率
//            String replyChance = "0";
//            if (sumCount > 0 && goodCount > 0) {
//                replyChance = String.format("%.2f", ((goodCount.doubleValue() / sumCount.doubleValue())));
//            }
//            // 评分星数(商品评星 + 服务评星)/2
//            Integer replyStar = 0;
//            if (sumCount > 0) {
//                replyStar = getSumStar(productId);
//
//            }
//            MyRecord record = new MyRecord();
//            record.set("sumCount", sumCount);
//            record.set("goodCount", goodCount);
//            record.set("mediumCount", mediumCount);
//            record.set("poorCount", poorCount);
//            record.set("replyChance", replyChance);
//            record.set("replyStar", replyStar);
//            return record;
//        }

        /**
         * H5商品详情评论信息
         *
         * @param proId 商品编号
         * @return ProductDetailReplyResponse
         */
//        @Override
//        public ProductDetailReplyResponse getH5ProductReply (Integer proId){
//            ProductDetailReplyResponse response = new ProductDetailReplyResponse();
//
//            // 评论总数
//            Integer sumCount = getCountByScore(proId, "all");
//            if (sumCount.equals(0)) {
//                response.setSumCount(0);
//                response.setReplyChance("0");
//                return response;
//            }
//            // 好评总数
//            Integer goodCount = getCountByScore(proId, "good");
//            // 好评率
//            String replyChance = "0";
//            if (sumCount > 0 && goodCount > 0) {
//                replyChance = String.format("%.2f", ((goodCount.doubleValue() / sumCount.doubleValue())));
//            }
//
//            // 查询最后一条评论
//            LambdaQueryWrapper<StoreProductReply> lqw = new LambdaQueryWrapper<>();
//            lqw.eq(StoreProductReply::getProductId, proId);
//            lqw.eq(StoreProductReply::getIsDel, false);
//            lqw.orderByDesc(StoreProductReply::getId);
//            lqw.last(" limit 1");
//            StoreProductReply storeProductReply = dao.selectOne(lqw);
//            ProductReplyResponse productReplyResponse = new ProductReplyResponse();
//            BeanUtils.copyProperties(storeProductReply, productReplyResponse);
//            // 评价图
//            productReplyResponse.setPics(VcaUtil.stringToArrayStr(storeProductReply.getPics()));
//            // 昵称
//            String nickname = storeProductReply.getNickname();
//            if (StrUtil.isNotBlank(nickname)) {
//                if (nickname.length() == 1) {
//                    nickname = nickname.concat("**");
//                } else if (nickname.length() == 2) {
//                    nickname = nickname.substring(0, 1) + "**";
//                } else {
//                    nickname = nickname.substring(0, 1) + "**" + nickname.substring(nickname.length() - 1);
//                }
//                productReplyResponse.setNickname(nickname);
//            }
//            // 星数 = （商品评星 + 服务评星） / 2
//            BigDecimal sumScore = new BigDecimal(storeProductReply.getProductScore() + storeProductReply.getServiceScore());
//            BigDecimal divide = sumScore.divide(BigDecimal.valueOf(2L), 0, BigDecimal.ROUND_DOWN);
//            productReplyResponse.setScore(divide.intValue());
//
//            response.setSumCount(sumCount);
//            response.setReplyChance(replyChance);
//            response.setProductReply(productReplyResponse);
//            return response;
//        }

        /**
         * 移动端商品评论列表
         *
         * @param proId            商品编号
         * @param type             评价等级|0=全部,1=好评,2=中评,3=差评
         * @param pageParamRequest 分页参数
         * @return PageInfo<ProductReplyResponse>
         */
//        @Override
//        public PageInfo<ProductReplyResponse> getH5List (Integer proId, Integer type, PageParamRequest pageParamRequest)
//        {
//            Page<StoreProductReply> startPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
//
//            //带 StoreProductReply 类的多条件查询
//            LambdaQueryWrapper<StoreProductReply> lqw = new LambdaQueryWrapper<>();
//            lqw.eq(StoreProductReply::getIsDel, false);
//            lqw.eq(StoreProductReply::getProductId, proId);
//            //评价等级|0=全部,1=好评,2=中评,3=差评
//            switch (type) {
//                case 1:
//                    lqw.apply(" (product_score + service_score) >= 8");
//                    break;
//                case 2:
//                    lqw.apply(" (product_score + service_score) < 8 and (product_score + service_score) > 4");
//                    break;
//                case 3:
//                    lqw.apply(" (product_score + service_score) <= 4");
//                    break;
//                default:
//                    break;
//
//            }
//            lqw.orderByDesc(StoreProductReply::getId);
//            List<StoreProductReply> replyList = dao.selectList(lqw);
//            List<ProductReplyResponse> responseList = new ArrayList<>();
//            for (StoreProductReply productReply : replyList) {
//                ProductReplyResponse productReplyResponse = new ProductReplyResponse();
//                BeanUtils.copyProperties(productReply, productReplyResponse);
//                // 评价图
//                productReplyResponse.setPics(VcaUtil.stringToArrayStr(productReply.getPics()));
//                // 昵称
//                String nickname = productReply.getNickname();
//                if (StrUtil.isNotBlank(nickname)) {
//                    if (nickname.length() == 1) {
//                        nickname = nickname.concat("**");
//                    } else if (nickname.length() == 2) {
//                        nickname = nickname.substring(0, 1) + "**";
//                    } else {
//                        nickname = nickname.substring(0, 1) + "**" + nickname.substring(nickname.length() - 1);
//                    }
//                    productReplyResponse.setNickname(nickname);
//                }
//                // 星数 = （商品评星 + 服务评星） / 2
//                BigDecimal sumScore = new BigDecimal(productReply.getProductScore() + productReply.getServiceScore());
//                BigDecimal divide = sumScore.divide(BigDecimal.valueOf(2L), 0, BigDecimal.ROUND_DOWN);
//                productReplyResponse.setScore(divide.intValue());
//
//                responseList.add(productReplyResponse);
//            }
//            return CommonPage.copyPageInfo(startPage, responseList);
//        }

        /**
         * 删除评论
         *
         * @param id 评论id
         * @return Boolean
         */
        @Override
        public Boolean delete (Integer id){
            LambdaUpdateWrapper<StoreProductReply> lmdUp = new LambdaUpdateWrapper<>();
            lmdUp.set(StoreProductReply::getIsDel, 1);
            lmdUp.eq(StoreProductReply::getId, id);
            return update(lmdUp);
        }

        /**
         * 商品评论回复
         *
         * @param request 回复参数
         */
        @Override
        public Boolean comment (StoreProductReplyCommentRequest request){
            LambdaUpdateWrapper<StoreProductReply> lup = new LambdaUpdateWrapper<>();
            lup.eq(StoreProductReply::getId, request.getIds());
            lup.set(StoreProductReply::getMerchantReplyContent, request.getMerchantReplyContent());
            lup.set(StoreProductReply::getMerchantReplyTime, DateUtil.getNowTime());
            lup.set(StoreProductReply::getIsReply, true);
            return update(lup);
        }

        // 获取统计数据（好评、中评、差评）
        private Integer getCountByScore (Integer productId, String type){
            LambdaQueryWrapper<StoreProductReply> lqw = new LambdaQueryWrapper<>();
            lqw.eq(StoreProductReply::getProductId, productId);
            lqw.eq(StoreProductReply::getIsDel, false);

            switch (type) {
                case "all":
                    break;
                case "good":
                    lqw.apply(" (product_score + service_score) >= 8");
                    break;
                case "medium":
                    lqw.apply(" (product_score + service_score) < 8 and (product_score + service_score) > 4");
                    break;
                case "poor":
                    lqw.apply(" (product_score + service_score) <= 4");
                    break;
            }
            return dao.selectCount(lqw);
        }

        /**
         * 如果所有的都已评价，那么订单完成
         *
         * @return Integer
         * <AUTHOR>
         * @since 2020-06-03
         */
        private void completeOrder (StoreProductReply storeProductReply, Integer count, StoreOrder
        storeOrder, List < StoreOrderInfo > storeOrderInfoList){
//            Integer replyCount = getReplyCountByEntity(storeProductReply, true);
//            if (replyCount.equals(count)) {
                //全部商品都已评价
                storeOrder.setStatus(Constants.ORDER_STATUS_INT_COMPLETE);
                storeOrderInfoService.updateBatchById(storeOrderInfoList);
                storeOrderService.updateById(storeOrder);
//                redisUtil.lPush(Constants.ORDER_TASK_REDIS_KEY_AFTER_COMPLETE_BY_USER, storeOrder.getId());
            }
//        }

        /**
         * 检测当前商品是否可以评论
         * <AUTHOR>
         * @since 2020-06-03
         * @return Integer
         */
//    private Integer checkIsReply(StoreProductReply storeProductReply) {
//
//        //查看商品信息
//        List<StoreOrderInfo> orderInfoVoList = storeOrderInfoService.getOrderListByOrderId(storeProductReply.getOid());
//        if (null == orderInfoVoList || orderInfoVoList.size() < 1) {
//            throw new VcaException("没有找到商品信息");
//        }
//
//        boolean findResult = false;
////        for (StoreOrderInfoOldVo orderInfoVo : orderInfoVoList) {
//////            Integer productId = orderInfoVo.getInfo().getInteger("product_id");
////            Integer productId = orderInfoVo.getInfo().getProductId();
////            if (productId < 1) {
////                continue;
////            }
//
//            if (storeProductReply.getProductId().equals(productId)) {
//                findResult = true;
//                break;
//            }
//        }
//
//        if (!findResult) {
//            throw new VcaException("没有找到商品信息");
//        }
//
//        //商品是否已评价
//        Integer replyCount = getReplyCountByEntity(storeProductReply, false);
//        if (replyCount > 0) {
//            throw new VcaException("该商品已评价");
//        }
//
//        return orderInfoVoList.size();
//    }

        /**
         * 根据商品id  订单id  用户id 获取评论信息
         *
         * @return Integer
         * <AUTHOR>
         * @since 2020-06-03
         */
        private Integer getReplyCountByEntity (StoreProductReply request,boolean isAll){
            LambdaQueryWrapper<StoreProductReply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(StoreProductReply::getOid, request.getOid());
//        .eq(StoreProductReply::getUnique, request.getUnique());
            if (null != request.getUid()) {
                lambdaQueryWrapper.eq(StoreProductReply::getUid, request.getUid());
            }
            if (!isAll) {
                lambdaQueryWrapper.eq(StoreProductReply::getProductId, request.getProductId());

            }
            return dao.selectCount(lambdaQueryWrapper);
        }

        private String getOrderTypeStr (StoreOrder storeOrder){
            String orderTypeFormat = "[{}订单]{}";
            String orderType = "";
//        // 核销
//        if (StrUtil.isNotBlank(storeOrder.getVerifyCode())) {
//            orderType = StrUtil.format(orderTypeFormat, "核销", "");
            if (storeOrder.getType().equals(0)) {
                orderType = StrUtil.format(orderTypeFormat, "课程", "");
            }
            if (storeOrder.getType().equals(1)) {
                orderType = StrUtil.format(orderTypeFormat, "套课", "");
            }
            if (storeOrder.getType().equals(2)) {
                orderType = StrUtil.format(orderTypeFormat, "讲座", "");
            }
            if (storeOrder.getType().equals(3)) {
                orderType = StrUtil.format(orderTypeFormat, "展览", "");
            }
            if (storeOrder.getType().equals(4)) {
                orderType = StrUtil.format(orderTypeFormat, "商品", "");
            }
            return orderType;
        }

    }

