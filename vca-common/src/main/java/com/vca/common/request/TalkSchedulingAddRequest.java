package com.vca.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 新增或修改讲座排期Request
 * <AUTHOR>
 * @date 2022/11/17   10:12
 */
@Data
public class TalkSchedulingAddRequest implements Serializable {

    private static final long serialVersionUID = 4984744568211865019L;

    @ApiModelProperty(value = "排期id")
    private Long id;

    @ApiModelProperty(value = "讲座id")
    private Long talkId;

    @ApiModelProperty(value = "排期日期")
    private String schedulingDate;

    @ApiModelProperty(value = "排期当天时间")
    private String schedulingTime;

    @ApiModelProperty(value = "讲座语言id")
    private Integer courseLanguageId;

    @ApiModelProperty(value = "讲座价格")
    private BigDecimal price;

    @ApiModelProperty(value = "限量")
    private Integer quota;

    @ApiModelProperty(value = "预约状态 0=不可预约 1=可预约")
    private Integer status;

    @ApiModelProperty(value = "讲座类型 1=线下讲座 2=线上讲座")
    private Integer talkType;
}
