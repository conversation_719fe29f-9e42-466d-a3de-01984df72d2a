<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vca.service.dao.instructor.InstructorDao">

    <!-- 1. 分页查询讲师主信息SQL -->
    <select id="findInstructorPage" resultType="com.vca.common.response.InstructorResponse">
        SELECT
        vi.id,
        vi.name,
        vi.email,
        vi.phone_code,
        vi.phone_number,
        vi.status,
        vi.create_time,
        vi.update_time
        FROM
        vca_instructor vi
        <where>
            vi.is_deleted = 0
            <if test="param.name != null and param.name != ''">
                AND vi.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="param.courseIds != null and param.courseIds.size() > 0">
                AND vi.id IN (
                SELECT DISTINCT vicr_sub.instructor_id
                FROM vca_instructor_course_relation vicr_sub
                WHERE vicr_sub.course_id IN
                <foreach collection="param.courseIds" item="courseId" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
                )
            </if>
        </where>
        ORDER BY vi.create_time DESC
    </select>

    <!-- 2. 批量查询类型SQL-->
    <select id="findTypesByInstructorIds" resultType="com.vca.common.response.InstructorTypeResponse">
        SELECT
        vitr.instructor_id AS instructorId,
        vit.id,
        vit.name
        FROM vca_instructor_type vit
        JOIN vca_instructor_type_relation vitr ON vit.id = vitr.type_id
        WHERE vitr.instructor_id IN
        <foreach collection="instructorIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 3. 批量查询课程SQL -->
    <select id="findCoursesByInstructorIds" resultType="com.vca.common.response.CourseResponse">
        SELECT
        vicr.instructor_id AS instructorId,
        vc.id,
        vc.name,
        vct.name AS typeName,
        vicr.id AS relationId
        FROM vca_course vc
        JOIN vca_instructor_course_relation vicr ON vc.id = vicr.course_id
        LEFT JOIN vca_category vct ON vc.course_type_id = vct.id
        WHERE vicr.instructor_id IN
        <foreach collection="instructorIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 4. 批量查询不可授课时间SQL-->
    <select id="findUnavailableTimesByInstructorIds" resultType="com.vca.common.response.UnavailableTimeResponse">
        SELECT
        viut.instructor_id AS instructorId,
        viut.id,
        viut.start_date AS startTime,
        viut.end_date AS endTime
        FROM vca_instructor_unavailable_time viut
        WHERE viut.instructor_id IN
        <foreach collection="instructorIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 5. 查询讲师列表（不分页）-->
    <select id="findInstructorList" resultType="com.vca.common.response.InstructorResponse">
        SELECT
        vi.id,
        vi.name,
        vi.email,
        vi.phone_code,
        vi.phone_number,
        vi.status,
        vi.create_time,
        vi.update_time
        FROM
        vca_instructor vi
        <where>
            vi.is_deleted = 0
            <if test="name != null and name != ''">
                AND vi.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="courseIds != null and courseIds.size() > 0">
                AND vi.id IN (
                SELECT DISTINCT vicr_sub.instructor_id
                FROM vca_instructor_course_relation vicr_sub
                WHERE vicr_sub.course_id IN
                <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
                )
            </if>
        </where>
        ORDER BY vi.create_time DESC
    </select>

</mapper>
