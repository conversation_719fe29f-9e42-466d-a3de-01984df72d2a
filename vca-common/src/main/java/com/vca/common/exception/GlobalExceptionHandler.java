package com.vca.common.exception;

import com.vca.common.result.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.util.Objects;

/**
 * 全局参数、异常拦截
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 拦截表单参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public CommonResult bindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        logger.info("Exception ==>{}", Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        return CommonResult.failed(ExceptionCodeEnum.VALIDATE_FAILED);
    }

    /**
     * 拦截JSON参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult bindException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        logger.info("Exception ==>{}", Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        return CommonResult.failed(ExceptionCodeEnum.VALIDATE_FAILED);
    }

    /**
     * 拦截参数类型不正确
     *
     * @param e
     * @return
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public CommonResult bindException(HttpMediaTypeNotSupportedException e) {
        logger.info("Exception===>{}", Objects.requireNonNull(e.getMessage()));
        return CommonResult.failed(ExceptionCodeEnum.PRAM_NOT_MATCH);
    }


    //声明要捕获的异常
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public <T> CommonResult<?> defaultExceptionHandler(Exception e) {
        e.printStackTrace();
        logger.error(e.getMessage());
        if (e instanceof VcaException) {
            if (ExceptionCodeEnum.UNAUTHORIZED.getMessage().equals(e.getMessage())){
                return CommonResult.unauthorized();
            }
            return CommonResult.failed(ExceptionCodeEnum.ERROR.getCode(), e.getMessage());
        }
        if (e instanceof MissingServletRequestParameterException) {
            return CommonResult.failed(ExceptionCodeEnum.PRAM_NOT_MATCH);
        }
        if (e instanceof SQLException) {
            return CommonResult.failed(ExceptionCodeEnum.ERROR, "SQL异常");
        }
        //未知错误
        return CommonResult.failed(ExceptionCodeEnum.ERROR);
    }
}
