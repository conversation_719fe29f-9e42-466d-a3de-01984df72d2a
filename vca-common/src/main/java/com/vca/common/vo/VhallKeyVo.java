package com.vca.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program VCA_Mini_Program
 * @description 微吼key值
 * @classname VhallKeyVo
 * @since 2022/12/29 10:42:18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VhallKeyVo {

    //排期id
    private Long scheduleId;

    //用户id
    private Integer uid;

    //key值
    private String key;

}
