package com.vca.service.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.vca.common.exception.VcaException;
import com.vca.common.model.system.SystemCity;
import com.vca.common.model.user.UserAddress;
import com.vca.common.request.PageParamRequest;
import com.vca.common.request.UserAddressRequest;
import com.vca.common.response.UserAddressResponse;
import com.vca.common.utils.CommonUtil;
import com.vca.common.utils.VcaUtil;
import com.vca.service.dao.user.UserAddressDao;
import com.vca.service.service.SystemCityService;
import com.vca.service.service.UserAddressService;
import com.vca.service.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * UserAddressServiceImpl 接口实现
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressDao, UserAddress> implements UserAddressService {

    @Resource
    private UserAddressDao dao;

    @Autowired
    private SystemCityService systemCityService;

    @Autowired
    private UserService userService;

    /**
    * 列表
    * @return List<UserAddress>
    */
    @Override
    public List<UserAddressResponse> getList(PageParamRequest pageParamRequest) {
        ArrayList<UserAddressResponse> userAddressResponses = new ArrayList<>();
        Integer UserId = userService.getUserIdException();
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserAddress> lqw = Wrappers.lambdaQuery();
        lqw.select(UserAddress::getId, UserAddress::getUid,UserAddress::getRealName, UserAddress::getPhone, UserAddress::getProvince,
                UserAddress::getCity, UserAddress::getDistrict, UserAddress::getDetail, UserAddress::getIsDefault);
        lqw.eq(UserAddress::getUid, UserId);
        lqw.eq(UserAddress::getIsDel, false);
        lqw.orderByDesc(UserAddress::getIsDefault);
        lqw.orderByDesc(UserAddress::getId);
        List<UserAddress> userAddresses = dao.selectList(lqw);

        if (CollUtil.isEmpty(userAddresses)){
            return userAddressResponses;
        }else {
            for (UserAddress userAddress : userAddresses) {
                UserAddressResponse userAddressResponse = new UserAddressResponse();
                BeanUtils.copyProperties(userAddress, userAddressResponse);
                List<String> address = new ArrayList<>();
                address.add(userAddress.getProvince());
                address.add(userAddress.getCity());
                address.add(userAddress.getDistrict());
                userAddressResponse.setAddress(address);
                userAddressResponses.add(userAddressResponse);
            }
        }
        return userAddressResponses;
    }

    /**
     * 创建地址
     * @param request UserAddressRequest 参数
     * @return List<UserAddress>
     */
    @Override
    public UserAddressResponse create(UserAddressRequest request) {
        UserAddressResponse userAddressResponse = new UserAddressResponse();
        UserAddress userAddress = new UserAddress();
        BeanUtils.copyProperties(request, userAddress);
        userAddress.setCity(request.getAddress().get(1));
        userAddress.setDistrict(request.getAddress().get(2));
        userAddress.setProvince(request.getAddress().get(0));
        userAddress.setUid(userService.getUserIdException());
        if (userAddress.getIsDefault()) {
            //把当前用户其他默认地址取消
            cancelDefault(userAddress.getUid());
        }
        saveOrUpdate(userAddress);

        BeanUtils.copyProperties(request,userAddressResponse);
        return userAddressResponse;
    }

    /**
     * 设置默认
     * @param id Integer id
     * @return UserAddress
     */
    @Override
    public UserAddressResponse def(Integer id) {
        if (ObjectUtil.isNull(id)){
            throw new VcaException("地址ID必传");
        }
        Integer uid = userService.getUserIdException();
        if (ObjectUtil.isEmpty(this.getOne(
                Wrappers.<UserAddress>lambdaQuery()
                        .eq(UserAddress::getId,id)
                        .eq(UserAddress::getUid,uid)
                        .eq(UserAddress::getIsDel,0)))){
            throw new VcaException("该地址不存在");
        }
        UserAddressResponse userAddressResponse = new UserAddressResponse();
        //把当前用户其他默认地址取消
        cancelDefault(uid);
        UserAddress userAddress = new UserAddress();
        userAddress.setId(id);
        userAddress.setUid(uid);
        userAddress.setIsDefault(true);
        updateById(userAddress);
        UserAddress userAddress1 = getById(id);
        BeanUtils.copyProperties(userAddress1,userAddressResponse);
        ArrayList<String> address = new ArrayList<>();
        address.add(userAddress1.getProvince());
        address.add(userAddress1.getCity());
        address.add(userAddress1.getDistrict());
        userAddressResponse.setAddress(address);
        return userAddressResponse;
    }

    /**
     * 删除
     * @param id Integer id
     * @return UserAddress
     */
    @Override
    public Boolean delete(Integer id) {
        //把当前用户其他默认地址取消
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getId, id);
        lambdaQueryWrapper.eq(UserAddress::getUid, userService.getUserIdException());
        UserAddress userAddress = dao.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(userAddress)){
            throw new VcaException("该地址不存在");
        }
        dao.delete(lambdaQueryWrapper);
        return Boolean.TRUE;
    }

    /**
     * 获取默认地址
     * @return UserAddress
     */
    @Override
    public UserAddressResponse getDefault() {
        UserAddressResponse userAddressResponse = new UserAddressResponse();
        //把当前用户其他默认地址取消
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getIsDefault, true);
        lambdaQueryWrapper.eq(UserAddress::getUid, userService.getUserId());
        UserAddress userAddress = dao.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(userAddress)){
            return new UserAddressResponse();
        }else {
            BeanUtils.copyProperties(userAddress, userAddressResponse);
            ArrayList<String> address = new ArrayList<>();
            address.add(userAddress.getProvince());
            address.add(userAddress.getCity());
            address.add(userAddress.getDistrict());
            userAddressResponse.setAddress(address);
        }
        return userAddressResponse;
    }

    @Override
    public UserAddress getById(Integer addressId) {
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getId, addressId);
        lambdaQueryWrapper.eq(UserAddress::getIsDel, false);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 获取地址详情
     * @param id 地址id
     * @return UserAddress
     */
    @Override
    public UserAddressResponse getDetail(Integer id) {
        UserAddressResponse userAddressResponse = new UserAddressResponse();
        Integer UserId = userService.getUserIdException();
        LambdaQueryWrapper<UserAddress> lqw = Wrappers.lambdaQuery();
        lqw.select(UserAddress::getId, UserAddress::getRealName, UserAddress::getPhone, UserAddress::getProvince,
                UserAddress::getCity, UserAddress::getDistrict, UserAddress::getDetail, UserAddress::getIsDefault);
        lqw.eq(UserAddress::getId, id);
        lqw.eq(UserAddress::getUid, UserId);
        lqw.eq(UserAddress::getIsDel, false);
        UserAddress userAddress = dao.selectOne(lqw);
        if (ObjectUtil.isNull(userAddress)){
            return new UserAddressResponse();
        }else {
            BeanUtils.copyProperties(userAddress, userAddressResponse);
            ArrayList<String> address = new ArrayList<>();
            address.add(userAddress.getProvince());
            address.add(userAddress.getCity());
            address.add(userAddress.getDistrict());
            userAddressResponse.setAddress(address);
        }

        return userAddressResponse;
    }

    /**
     * 获取默认地址
     * @return UserAddress
     */
    @Override
    public UserAddressResponse getDefaultByUid(Integer uid) {
        UserAddressResponse userAddressResponse = new UserAddressResponse();
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserAddress::getIsDefault, true);
        lambdaQueryWrapper.eq(UserAddress::getUid, uid);
        UserAddress userAddress = dao.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(userAddress)){
            return new UserAddressResponse();
        }else {
            BeanUtils.copyProperties(userAddress, userAddressResponse);
            ArrayList<String> address = new ArrayList<>();
            address.add(userAddress.getProvince());
            address.add(userAddress.getCity());
            address.add(userAddress.getDistrict());
            userAddressResponse.setAddress(address);
        }

        return userAddressResponse;
    }

    /**
     * 检测城市id是否合法
     * @param cityId Integer 城市id
     */
    private void checkCity(Integer cityId) {
        //检测城市Id是否存在
        SystemCity systemCity = systemCityService.getCityByCityId(cityId);
        if (ObjectUtil.isNull(systemCity)) {
            throw new VcaException("请选择正确的城市");
        }
    }

    /**
     * 取消默认地址
     * @param userId Integer 城市id
     */
    private void cancelDefault(Integer userId) {
        //检测城市Id是否存在
        UserAddress userAddress = new UserAddress();
        userAddress.setIsDefault(false);
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getUid, userId);
        update(userAddress, lambdaQueryWrapper);
    }

}

