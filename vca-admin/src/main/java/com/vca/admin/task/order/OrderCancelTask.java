package com.vca.admin.task.order;

import com.vca.common.utils.DateUtil;
import com.vca.service.service.OrderTaskService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 用户取消订单task任务
 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class OrderCancelTask {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(OrderCancelTask.class);

    @Autowired
    private OrderTaskService orderTaskService;

    private static final String SIXTY_MINUTE = "PT1M";
    private static final String THREE_MINUTE = "PT1M";

    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    @SchedulerLock(name = "OrderCancelTask_dev", lockAtMostFor = SIXTY_MINUTE, lockAtLeastFor = THREE_MINUTE)
    public void init() {
        logger.info("---OrderCancelTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            orderTaskService.cancelByUser();

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("OrderCancelTask.task" + " | msg : " + e.getMessage());
        }

    }
}
