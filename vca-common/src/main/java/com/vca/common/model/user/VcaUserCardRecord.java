package com.vca.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 礼品卡领取/赠送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VcaUserCardRecord对象", description="礼品卡领取/赠送记录表")
public class VcaUserCardRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "礼品卡领取/赠送记录表ID")
      @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "赠送人")
    private Integer sendUser;

    @ApiModelProperty(value = "领取人")
    private Integer getUser;

    @ApiModelProperty(value = "礼品卡ID")
    private Integer cardId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
