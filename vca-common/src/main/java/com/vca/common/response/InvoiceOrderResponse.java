package com.vca.common.response;

import com.vca.common.vo.PreOrderCommonVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 后台发票订单响应类
 * @date 2023-02-13 09:48
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InvoiceOrderResponse对象", description="后台发票订单响应类")
public class InvoiceOrderResponse implements Serializable {

    private static final long serialVersionUID = 7207869170471554505L;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("总金额")
    private BigDecimal price;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty(value = "商品信息")
    private List<PreOrderCommonVo.PreOrderProductDetail> orderProductDetail;
}
