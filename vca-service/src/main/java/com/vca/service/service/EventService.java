package com.vca.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vca.common.model.event.Event;
import com.vca.common.page.CommonPage;
import com.vca.common.request.PageParamRequest;
import com.vca.common.response.EventResponse;

import java.util.List;
import java.util.Map;

public interface EventService extends IService<Event> {
    CommonPage<Event> eventList(PageParamRequest pageParamRequest, String status,String eventDate);

    boolean addEvent(Event event);

    boolean editEvent(Event event);

    Map<String,List<EventResponse>> getMapList(String date);
}
