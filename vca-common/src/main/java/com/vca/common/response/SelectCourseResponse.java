package com.vca.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 选择课程响应对象
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@ApiModel(value = "选择课程响应对象")
public class SelectCourseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程分类ID")
    private Integer id;

    @ApiModelProperty(value = "课程分类名称")
    private String name;

    @ApiModelProperty(value = "课程分类名称(英文)")
    private String nameEn;

    @ApiModelProperty(value = "课程列表")
    private List<CourseItem> courseList = new ArrayList<>();

    @Data
    public static class CourseItem {
        @ApiModelProperty(value = "课程ID")
        private Long id;

        @ApiModelProperty(value = "课程名称")
        private String name;

        @ApiModelProperty(value = "课程名称(英文)")
        private String nameEn;
    }
} 