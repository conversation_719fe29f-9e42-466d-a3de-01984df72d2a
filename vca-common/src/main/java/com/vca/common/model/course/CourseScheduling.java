package com.vca.common.model.course;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

/**
 * <p>
 * 课程排课表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@TableName("vca_course_scheduling")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VcaCourseScheduling对象", description="课程排课表")
public class CourseScheduling implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "排课主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "排课日期")
    private String schedulingDate;

    @ApiModelProperty(value = "排课当天开始时间")
    private String schedulingStartTime;

    @ApiModelProperty(value = "排课当天结束时间")
    private String schedulingEndTime;

    @ApiModelProperty(value = "课程语言id")
    private Integer courseLanguageId;

    @ApiModelProperty(value = "课程价格ID")
    private Integer priceId;

    @ApiModelProperty(value = "席位")
    private Integer seatCount;

    @ApiModelProperty(value = "外部限量")
    private Integer externalLimit;

    @ApiModelProperty(value = "外部预约")
    private Integer externalAppointment;

    @ApiModelProperty(value = "外部上课人数")
    private Integer externalStudentsNumber;

    @ApiModelProperty(value = "内部限量")
    private Integer internalLimit;

    @ApiModelProperty(value = "内部预约")
    private Integer internalAppointment;

    @ApiModelProperty(value = "内部上课人数")
    private Integer internalStudentsNumber;

    @ApiModelProperty(value = "预约状态")
    private Boolean status;

//    @ApiModelProperty(value = "课程状态 0=已结束 1=进行中 2=未开始")
//    private Integer schedulingStatus;

    @ApiModelProperty("是否取消 0=正常 1取消")
    private Integer isCancel;

    @ApiModelProperty("取消原因")
    private String cancellationReasons;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "课程地址")
    @TableField(exist = false)
    private HashMap<String, Object> courseAddress;

    @ApiModelProperty(value = "课程语言")
    @TableField(exist = false)
    private String courseLanguageName;

    @ApiModelProperty(value = "剩余席位")
    @TableField(exist = false)
    private Integer remainingSeatCount;

    @ApiModelProperty(value = "课程价格")
    @TableField(exist = false)
    private BigDecimal price;


}
