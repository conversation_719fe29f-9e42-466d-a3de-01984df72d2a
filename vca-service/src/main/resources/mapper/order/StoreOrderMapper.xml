<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vca.service.dao.order.StoreOrderDao">

    <select id="getTotalPrice" resultType="java.math.BigDecimal">
        select sum(pay_price)
        from vca_order
        where ${where}
          and paid = 1
          and is_del=0
    </select>
    <select id="getRefundPrice" resultType="java.math.BigDecimal">
        select sum(refund_price)
        from vca_store_order
        where ${where}
          and refund_status = 2
    </select>
    <select id="getRefundTotal" resultType="java.lang.Integer">
        select count(id)
        from vca_store_order
        where ${where}
          and refund_status = 2
    </select>
    <select id="getOrderVerificationDetail" parameterType="com.vca.common.request.PageParamRequest"
            resultType="com.vca.common.response.StoreStaffDetail">
        select sum(o.`pay_price`) as price, count(o.`id`) as count, DATE_FORMAT(o.`create_time`, '%Y-%m-%d') as time
        from `vca_store_order` o
        where o.`is_del` = 0 and o.`paid` = 1 and o.`refund_status` = 0
        <if test="null != startTime and startTime != ''">
            and o.create_time >= #{ startTime }
        </if>
        <if test="null != endTime and endTime != ''">
            and o.create_time &lt; #{ endTime }
        </if>
        GROUP by DATE_FORMAT(o.`create_time`, '%Y-%m-%d') order by o.`create_time` desc limit #{ page },#{ limit };
    </select>
    <select id="getOrderStatisticsPriceDetail" parameterType="com.vca.common.request.StoreDateRangeSqlPram"
            resultType="com.vca.common.response.StoreOrderStatisticsChartItemResponse">
        select sum(o.pay_price) as num, date_format(o.create_time, '%Y-%m-%d') as time
        from vca_store_order o
        where o.is_del >= 0
          and o.paid >= 1
          and o.refund_status >= 0
          and o.create_time >= #{ startTime }
          and o.create_time
         &lt; #{ endTime }
        group by date_format(o.create_time, '%Y-%m-%d')
        order by o.create_time desc;
    </select>
    <select id="getOrderStatisticsOrderCountDetail" parameterType="com.vca.common.request.StoreDateRangeSqlPram"
            resultType="com.vca.common.response.StoreOrderStatisticsChartItemResponse">
        select count(id) as num, date_format(o.create_time, '%Y-%m-%d') as time
        from vca_store_order o
        where o.is_del >= 0
          and o.paid >= 1
          and o.refund_status >= 0
          and o.create_time >= #{ startTime }
          and o.create_time
         &lt; #{ endTime }
        group by date_format(o.create_time, '%Y-%m-%d')
        order by o.create_time asc;
    </select>

    <select id="getBrokerageData" resultType="com.vca.common.request.OrderBrokerageData">
        select count(*) as num, sum(o.pay_price) as price
        from vca_store_order as o
                 right join vca_user_brokerage_record as br
                            on br.link_id = o.order_id and br.status = 3 and br.uid = #{spreadId}
        where o.uid = #{uid}
          and o.status > 1;
    </select>


    <select id="getCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM vca_order
        WHERE order_id IN (SELECT order_id
                           FROM vca_order_info
                           WHERE lecturer = #{userId}
                             AND is_get = 1
                             AND scheduling_end_time > #{currentTime})
          AND type = #{type}
          AND STATUS = 1
    </select>
    <select id="getMonthIsCompleteOrder" resultType="com.vca.common.model.order.StoreOrder">
        SELECT *
        FROM vca_order
        WHERE `paid` = 1
          AND uid = #{userId}
          AND refund_status != 3
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')
    </select>


    <select id="getAppointment" resultType="com.vca.common.response.MyAppointmentItemResponse">
        SELECT
        a.main_id AS id,
        a.type AS mainType,
        a.NAME AS NAME,
        a.scheduling_date,
        a.scheduling_start_time,
        a.scheduling_end_time,
        b.STATUS AS type
        FROM
        vca_order_info a
        LEFT JOIN vca_order b ON a.order_no = b.order_id
        WHERE
        b.STATUS = #{type}
        AND a.lecturer = #{userId}
        <if test="mainType == 0">
            AND a.type in (0,1)
        </if>

        <if test="mainType == 2">
            AND a.type = #{mainType}
        </if>

        <if test="mainType == 3">
            AND a.type = #{mainType}
        </if>
        AND a.is_get = 1
        ORDER BY a.scheduling_date,
        a.scheduling_start_time DESC
    </select>
    <select id="getTalkOrderByScheduleId" resultType="com.vca.common.model.order.StoreOrder">
        SELECT
            t1.*
        FROM
            vca_order t1
                LEFT JOIN vca_order_info t2 ON t1.order_id = t2.order_no
        WHERE
            t2.buyer = #{uid}
          AND t2.type =2
          AND t2.main_id = #{talkId}
          AND t1.is_del = 0
          AND t2.refund_status = 0
          AND t1.`status` >= 1
    </select>
    <select id="detailClient" parameterType="com.vca.common.request.StoreDateRangeSqlPram" resultType="com.vca.common.vo.DetailClientVo">
        SELECT
            any_value ( t1.uid ) uid,
            any_value ( t1.orderId ) orderId,
            any_value ( t1.mainId ) mainId,
            any_value ( t1.type ) type,
            any_value ( t1.NAME ) NAME,
            any_value ( t1.orderNo ) orderNo,
            any_value ( t1.periods ) periods,
            any_value ( t1.payTime ) payTime,
            any_value ( t1.writeOffTime ) writeOffTime,
            any_value ( t1.payPrice ) payPrice,
            any_value ( t1.payPrice ) totalPayPrice,
            any_value ( t1.payType ) payType,
            any_value ( t1.schedulingDate ) schedulingDate,
            any_value ( t1.schedulingPrice ) schedulingPrice,
            any_value ( t1.STATUS ) STATUS,
            any_value ( t1.orderInfoId ) orderInfoId,
            any_value ( t1.refundPrice ) refundPrice,
            any_value ( t1.refundTime ) refundTime,
            any_value ( t1.refundStatus ) refundStatus,
            any_value ( t1.userType ) userType,
            any_value ( t1.creditNoteId ) creditNoteId,
            any_value ( t1.payNum ) payNum,
            any_value ( t1.merOrderId ) merOrderId ,
            any_value ( t4.create_time ) invoiceTime,
            any_value ( t4.price ) invoicePrice,
            any_value ( t5.nickname ) nickName,
            any_value ( t6.price ) creditNotePrice,
            any_value ( t6.create_time ) creditNoteTime,
            any_value ( t1.umsMerOrderId ) umsMerOrderId,
            any_value ( t7.card_no ) cardNo,
            any_value ( t7.id ) cardId,
            any_value ( t7.use_time ) useTime ,
            any_value ( t7.uid ) cardUid ,
            any_value ( t7.`stutus` ) cardStatus,
            any_value ( t8.card_no ) couponNo
        FROM
            (
                SELECT
                    any_value ( t1.uid ) uid,
                    any_value ( t1.id ) orderId,
                    GROUP_CONCAT( t2.main_id ) mainId,
                    any_value ( t1.type ) type,
                    GROUP_CONCAT( t2.NAME ) NAME,
                    any_value ( t1.order_id ) orderNo,
                    COUNT( t2.main_id ) periods,
                    any_value ( t1.pay_time ) payTime,
                    any_value ( t1.pay_price ) payPrice,
                    any_value ( t1.pay_type ) payType,
                    any_value ( t1.coupon_id ) couponId,
                    GROUP_CONCAT( t2.scheduling_date ) schedulingDate,
                    GROUP_CONCAT( t2.pay_price ) schedulingPrice,
                    GROUP_CONCAT( t2.STATUS ) STATUS,
                    GROUP_CONCAT( t2.id ) orderInfoId,
                    any_value ( t1.refund_price ) refundPrice,
                    any_value ( t1.refund_reason_time ) refundTime,
                    any_value ( t1.refund_status ) refundStatus,
                    any_value ( t1.user_type ) userType,
                    any_value (t2.pay_num ) payNum,
                    group_concat(LEFT ( t2.write_off_time, 10 )) writeOffTime,
                    GROUP_CONCAT( t2.credit_note_id ) creditNoteId,
                    any_value ( t1.ums_mer_order_id ) umsMerOrderId,
                    any_value ( t2.mer_order_no ) merOrderId
                FROM
                    vca_order t1
                        LEFT JOIN vca_order_info t2 ON t1.order_id = t2.order_no
                WHERE
                    t1.create_time BETWEEN #{ startTime }  AND #{ endTime }
                  and t1.paid =1
                GROUP BY
                    t2.order_no
            ) t1
                LEFT JOIN vca_invoice_order_relation t3 ON t3.order_id = t1.orderId
                LEFT JOIN vca_invoice_record t4 ON t3.invoice_id = t4.id
                LEFT JOIN vca_user t5 ON t5.uid = t1.uid
                LEFT JOIN vca_invoice_credit_note t6 ON t6.id IN ( t1.creditNoteId )
                LEFT JOIN vca_user_card t7 ON t7.order_id = t1.merOrderId
                LEFT JOIN vca_user_card t8 ON t8.id = t1.couponId
        GROUP BY
            t1.orderNo
        ORDER BY
            t1.uid;
    </select>
    <select id="testDetailClient" parameterType="com.vca.common.request.StoreDateRangeSqlPram" resultType="com.vca.common.vo.DetailClientVo">
        SELECT
            any_value ( t1.uid ) uid,
            any_value ( t1.orderId ) orderId,
            any_value ( t1.mainId ) mainId,
            any_value ( t1.type ) type,
            any_value ( t1.NAME ) NAME,
            any_value ( t1.orderNo ) orderNo,
            any_value ( t1.periods ) periods,
            any_value ( t1.payTime ) payTime,
            any_value ( t1.writeOffTime ) writeOffTime,
            any_value ( t1.payPrice ) payPrice,
            any_value ( t1.payPrice ) totalPayPrice,
            any_value ( t1.payType ) payType,
            any_value ( t1.schedulingDate ) schedulingDate,
            any_value ( t1.schedulingPrice ) schedulingPrice,
            any_value ( t1.STATUS ) STATUS,
            any_value ( t1.orderInfoId ) orderInfoId,
            any_value ( t1.refundPrice ) refundPrice,
            any_value ( t1.refundTime ) refundTime,
            any_value ( t1.refundStatus ) refundStatus,
            any_value ( t1.userType ) userType,
            any_value ( t1.creditNoteId ) creditNoteId,
            any_value ( t1.payNum ) payNum,
            any_value ( t1.merOrderId ) merOrderId,
            any_value ( t4.create_time ) invoiceTime,
            any_value ( t4.price ) invoicePrice,
            any_value ( t5.nickname ) nickName,
            any_value ( t6.price ) creditNotePrice,
            any_value ( t6.create_time ) creditNoteTime,
            any_value ( t1.umsMerOrderId ) umsMerOrderId,
            any_value ( t7.card_no ) cardNo,
            any_value ( t7.id ) cardId,
            any_value ( t7.use_time ) useTime,
            any_value ( t7.uid ) cardUid,
            any_value ( t7.`stutus` ) cardStatus,
            any_value ( t8.card_no ) couponNo,
            any_value ( t1.masterWaybillNo ) masterWaybillNo,
            any_value ( t1.deliveryTime ) deliveryTime,
            any_value ( t1.sendType ) sendType,
            any_value ( t1.recommendUserId ) recommendUserId,
            any_value ( t1.recommendUserName ) recommendUserName,
            any_value (t4.invoice_no) invoiceNo,
            any_value ( t1.remark ) remark
        FROM
            (
                SELECT
                    t1.uid uid,
                    t1.id orderId,
                    t2.main_id mainId,
                    t1.type type,
                    t2.NAME NAME,
                    t1.order_id orderNo,
                    t2.main_id periods,
                    t1.pay_time payTime,
                    t1.pay_price payPrice,
                    t1.pay_type payType,
                    t1.coupon_id couponId,
                    t2.scheduling_date schedulingDate,
                    t2.pay_price schedulingPrice,
                    t2.STATUS STATUS,
                    t2.id orderInfoId,
                    t2.refund_price refundPrice,
                    t2.refund_reason_time refundTime,
                    t2.refund_status refundStatus,
                    t1.user_type userType,
                    t2.pay_num payNum,
                    t2.write_off_time writeOffTime,
                    t2.credit_note_id creditNoteId,
                    t1.ums_mer_order_id umsMerOrderId,
                    t2.mer_order_no merOrderId,
                    t1.master_waybill_no masterWaybillNo,
                    t1.delivery_time deliveryTime,
                    t1.send_type sendType,
                    t1.recommend_user_id recommendUserId,
                    t1.recommend_user_name recommendUserName,
                    t1.remark remark
                FROM
                    vca_order t1
                        LEFT JOIN vca_order_info t2 ON t1.order_id = t2.order_no
                WHERE
                    t1.create_time BETWEEN #{ startTime }  AND #{ endTime }
                  AND t1.paid = 1
            ) t1
                LEFT JOIN vca_invoice_order_relation t3 ON t3.order_id = t1.orderId
                LEFT JOIN vca_invoice_record t4 ON t3.invoice_id = t4.id
                LEFT JOIN vca_user t5 ON t5.uid = t1.uid
                LEFT JOIN vca_invoice_credit_note t6 ON t6.id IN ( t1.creditNoteId )
                LEFT JOIN vca_user_card t7 ON t7.order_id = t1.merOrderId
                LEFT JOIN vca_user_card t8 ON t8.id = t1.couponId
        ORDER BY
            t1.payTime desc
    </select>

    <select id="detailClientOrderByInfo" resultType="com.vca.common.vo.DetailClientVo">
        SELECT
            any_value ( t1.uid ) uid,
            any_value ( t1.id ) orderId,
            GROUP_CONCAT( t2.main_id ) mainId,
            any_value ( t1.type ) type,
            GROUP_CONCAT( t2.NAME ) NAME,
            any_value ( t1.order_id ) orderNo,
            COUNT( t2.main_id ) periods,
            any_value ( t1.pay_time ) payTime,
            any_value ( t1.pay_price ) payPrice,
            any_value ( t1.pay_type ) payType,
            GROUP_CONCAT( t2.scheduling_date ) schedulingDate,
            GROUP_CONCAT( t4.pay_price/t4.pay_num ) schedulingPrice,
            any_value ( t1.pay_price ) totalPayPrice,
            GROUP_CONCAT( t2.STATUS ) STATUS,
            GROUP_CONCAT( t2.id ) orderInfoId,
            group_concat(LEFT ( t2.write_off_time, 10 )) writeOffTime,
            any_value ( t1.refund_price ) refundPrice,
            any_value ( t1.refund_reason_time ) refundTime,
            any_value ( t1.refund_status ) refundStatus,
            any_value ( t1.user_type ) userType,
            GROUP_CONCAT( t2.credit_note_id ) creditNoteId,
            any_value ( t1.ums_mer_order_id ) umsMerOrderId,
            any_value ( t1.coupon_id ) couponId,
            any_value ( t2.mer_order_no ) merOrderId
        FROM
            vca_user_card t3
            LEFT JOIN vca_order_info t4 ON t4.mer_order_no = t3.order_id
            LEFT JOIN vca_order t1 ON t1.coupon_id=t3.id
            LEFT JOIN vca_order_info t2 ON t1.order_id = t2.order_no
        WHERE
        t3.id IN
        <foreach collection="cardList" item="card" index="index" open="(" close=")" separator=",">
            #{card}
        </foreach>
          and t1.paid =1
        GROUP BY
            t2.order_no
    </select>
    <select id="summaryDay" resultType="com.vca.common.model.order.StoreOrder">
        SELECT
            SUM( t4.pay_price ) payPrice,
            any_value (
                LEFT ( t3.write_off_time, 10 )) closingTime
        FROM
            vca_order t1
                LEFT JOIN vca_user_card t2 ON t1.coupon_id = t2.id
                LEFT JOIN vca_order_info t3 ON t3.order_no = t1.order_id
                LEFT JOIN vca_order_info t4 ON t4.mer_order_no = t2.order_id
        WHERE
                t1.coupon_id != 0
	        and t4.pay_num=1
	        AND t3.write_off_time BETWEEN #{start} AND #{end}
            GROUP BY
                LEFT ( t3.write_off_time, 10 );
    </select>
    <select id="productClosing" resultType="com.vca.common.model.order.StoreOrder">
        SELECT
            t2.pay_price  payPrice,
            LEFT ( t1.closing_time, 10 ) closingTime,
            t2.mer_order_no orderId
        FROM
            vca_order t1
            LEFT JOIN vca_order_info t2 ON t2.order_no = t1.order_id
        WHERE
            t1.type = 4
          AND t1.closing_time BETWEEN #{start} AND #{end}
        GROUP BY
            LEFT ( t1.closing_time, 10 );
    </select>
    <select id="productPay" resultType="com.vca.common.model.order.StoreOrder">
        SELECT
            t2.pay_price  payPrice,
            LEFT ( t1.pay_time, 10 ) payTime,
            t2.mer_order_no orderId
        FROM
            vca_order t1
            LEFT JOIN vca_order_info t2 ON t2.order_no = t1.order_id
        WHERE
            t1.type = 4
            and t1.paid=1
          AND t1.pay_time BETWEEN #{start} AND #{end}
    </select>
    <select id="cardPay" resultType="com.vca.common.model.order.StoreOrder">
        SELECT
            SUM( t3.pay_price/t3.pay_num ) payPrice,
            any_value (
                LEFT ( t1.pay_time, 10 )) payTime
        FROM
            vca_order t1
                LEFT JOIN vca_order_info t3 ON t3.order_no = t1.order_id
                LEFT JOIN vca_user_card t2 ON t2.order_id = t3.mer_order_no
        WHERE
            t1.type = 4
          and t2.id!=0
          AND t1.pay_time BETWEEN #{start} AND #{end}
        GROUP BY
            LEFT ( t1.pay_time, 10 );
    </select>

</mapper>
