package com.vca.service.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vca.common.model.exhibit.Exhibit;
import com.vca.common.request.PageParamRequest;
import com.vca.service.dao.exhibit.ExhibitDao;
import com.vca.service.service.ExhibitService;
import com.vca.service.service.SystemAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @ClassName ExhibitServiceImpl
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2025/4/2 15:16
 **/
@Service
public class ExhibitServiceImpl extends ServiceImpl<ExhibitDao, Exhibit> implements ExhibitService {

    @Resource
    private ExhibitDao exhibitDao;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Override
    public List<Exhibit> getList(Exhibit exhibit, PageParamRequest pageParamRequest) {

        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(Exhibit::getId, 89);
        queryWrapper.orderByDesc(Exhibit::getExhibitNo);
        List<Exhibit> exhibits = exhibitDao.selectList(queryWrapper);

        return exhibits;
    }

    @Override
    public boolean addExhibit(Exhibit exhibit) {
        exhibit.setCarouselImg(systemAttachmentService.clearPrefix(exhibit.getCarouselImg()));
        exhibit.setExhibitCarouselImg(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImg()));
        exhibit.setExhibitCarouselImgS(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImgS()));
        exhibit.setExhibitCarouselImgSL(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImgSL()));
        exhibit.setListImg(systemAttachmentService.clearPrefix(exhibit.getListImg()));
        exhibit.setExhibitAudio(systemAttachmentService.clearPrefix(exhibit.getExhibitAudio()));
        exhibit.setCreateTime(new Date());
        return save(exhibit);
    }

    @Override
    public boolean editExhibit(Exhibit exhibit) {
        exhibit.setCarouselImg(systemAttachmentService.clearPrefix(exhibit.getCarouselImg()));
        exhibit.setExhibitCarouselImg(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImg()));
        exhibit.setExhibitCarouselImgS(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImgS()));
        exhibit.setExhibitCarouselImgSL(systemAttachmentService.clearPrefix(exhibit.getExhibitCarouselImgSL()));
        exhibit.setListImg(systemAttachmentService.clearPrefix(exhibit.getListImg()));
        exhibit.setExhibitAudio(systemAttachmentService.clearPrefix(exhibit.getExhibitAudio()));
        exhibit.setUpdateTime(new Date());
        return updateById(exhibit);
    }

    @Override
    public Map<Integer, List<Exhibit>> getMapList() {
        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
        List<Exhibit> exhibits = exhibitDao.selectList(queryWrapper);

        Map<Integer, List<Exhibit>> exhibitMap = exhibits.stream()
                .collect(Collectors.groupingBy(Exhibit::getFloorNo));

        return exhibitMap;
    }

    @Override
    public Map<String, List<Exhibit>> getExhibitList() {
        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
        List<Exhibit> exhibits = exhibitDao.selectList(queryWrapper);

        Map<String, List<Exhibit>> exhibitMap = exhibits.stream()
                .collect(Collectors.groupingBy(Exhibit::getShowroom));

        return exhibitMap;
    }

    @Override
    public List<Exhibit> getShowroomList(Integer floorNo) {
        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Exhibit::getFloorNo, floorNo);
        List<Exhibit> exhibits = exhibitDao.selectList(queryWrapper);
        if (!exhibits.isEmpty()) {
            return exhibits.stream()
                    .filter(distinctByKey(Exhibit::getShowroom))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<Exhibit> getExhibitLists(String showroomId) {
        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Exhibit::getShowroom, showroomId);
        List<Exhibit> exhibits = exhibitDao.selectList(queryWrapper);
        return exhibits;
    }

    @Override
    public Exhibit searchExhibit(String exhibitNo) {
        LambdaQueryWrapper<Exhibit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Exhibit::getExhibitNo, exhibitNo);
        Exhibit exhibits = exhibitDao.selectOne(queryWrapper);
        return exhibits;
    }

    // 自定义去重工具方法
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
