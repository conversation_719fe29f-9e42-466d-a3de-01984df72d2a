package com.vca.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 下单VO类
 * @Author: Li
 * @Date: 2022/10/27 14:42
 */
@Data
public class UmsPayVo implements Serializable {

    private static final long serialVersionUID = -4170956320806233666L;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 报文请求时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String requestTimestamp;

    /**
     * 商户订单号
     */
    private String merOrderId;

    /**
     * 支付订单号
     */
    private String targetOrderId;

    /**
     * 请求系统预留字段
     */
    private String srcReserve;

    /**
     * 商户号
     */
    private String mid;

    /**
     * 终端号
     */
    private String tid;

    /**
     * 机构商户号
     */
    private String instMid;

    /**
     * 商品信息
     */
    private List<Goods> goods;

    /**
     * 商户附加数据
     */
    private String attachedData;

    /**
     * 订单过期时间  为空则使用系统默认过期时间（30分钟），格式：yyyy-MM-dd HH:mm:ss
     */
    private String expireTime;

    /**
     * 商品标记
     */
    private String goodsTag;

    /**
     * 账单描述
     */
    private String orderDesc;

    /**
     * 订单原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 支付总金额
     */
    private BigDecimal totalAmount;

    /**
     * 退款金额
     */
    private Integer refundAmount;

    /**
     * 退款订单号
     */
    private String refundOrderId;

    /**
     * 退货说明
     */
    private String refundDesc;

    /**
     * 分账标记 若为true，则goods字段和subOrders字段不能同时为空；
     * 且secureTransaction字段上送false或不上送
     */
    private Boolean divisionFlag;

    /**
     * 异步分账标记
     */
    private Boolean asynDivisionFlag;

    /**
     * 平台商户分账金额
     */
    private BigDecimal platformAmount;

    /**
     * 子订单信息
     */
    private List<SubOrders> subOrders;

    /**
     * 支付结果通知地址
     */
    private String notifyUrl;

    /**
     * 网页跳转地址
     */
    private String returnUrl;

    /**
     * 订单展示页面
     */
    private String showUrl;

    /**
     * 担保交易标识 取值：true或 false，默认false若上送为true，则交易的金额将会被暂缓结算。
     * 调用担保完成接口后，完成部分金额会在t+1日结算给商户，剩余部分金额退还用户。
     * 调用担保撤销接口，则全部资金退还给用户。
     * 30天后没有主动调用担保完成 且 没有主动调用担保撤销的交易 将会自动按撤销处理。
     */
    private String secureTransaction;

    /**
     * 微信子商户appId
     */
    private String subAppId;

    /**
     * 用户子标识 微信必传，需要商户自行调用微信平台接口获取，具体获取方式请根据微信接口文档。
     */
    private String subOpenId;

    /**
     * 用户子标识 支付宝必传，需要商户自行调用支付宝接口获取，具体获取方式请根据支付宝接口文档
     */
    private String userId;

    /**
     * 交易类型 值为MINI
     */
    private String tradeType;

    /**
     * 是否需要限制信用卡支付
     */
    private String limitCreditCard;

    /**
     * 花呗分期数  取值 仅支持 3,6,12
     */
    private Integer installmentNumber;

    /**
     * 预授权交易标识  取值：true或false，默认false。 若上送为true，则交易的金额将会被银行冻结。
     * 调用预授权完成接口后，完成部分金额会在t+1日结算给商户，剩余部分金额在用户银行卡中解冻。
     * （仅云闪付小程序支持）
     */
    private Boolean preauthTransaction;

    @Data
    public class Goods implements Serializable {
        /** 商品ID */
        private String goodsId;
        /** 商品名称 */
        private String goodsName;
        /** 商品数量  */
        private Integer quantity;
        /** 商品单价 */
        private BigDecimal price;
        /** 商品分类 */
        private String goodsCategory;
        /** 商品说明 */
        private String body;
        /** 商品单位 */
        private String unit;
        /** 商品折扣 */
        private BigDecimal discount;
        /** 子商户号 */
        private String subMerchantId;
        /** 商户子订单号 */
        private String merOrderId;
        /** 子商户商品总额 */
        private Integer subOrderAmount;
    }


    @Data
    public static class SubOrders implements Serializable {

        private static final long serialVersionUID = 7217830096025640211L;

        /** 子商户号 */
        private String mid;
        /** 商户子订单号 */
        private String merOrderId;
        /** 子商户分账金额 */
        private BigDecimal totalAmount;
    }

}
