//package com.vca.common.utils;
//
//import com.alibaba.fastjson.JSON;
//
//import com.alibaba.fastjson.JSONObject;
//import com.vca.common.constants.SFConstants;
//import com.vca.common.exception.VcaException;
//import com.vca.common.model.sf.MsgData;
//import com.vca.common.model.sf.MsgDataExpress;
//import com.vca.common.model.sf.MsgDataIntercept;
//import com.vca.common.model.sf.MsgDataUpdate;
//import com.vca.common.response.sf.ExpressResponse;
//import com.vca.common.response.sf.ResponseCancel;
//import com.vca.common.response.sf.SFReturnData;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.util.StringUtils;
//import sun.misc.BASE64Encoder;
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
//import java.security.MessageDigest;
//import java.security.NoSuchAlgorithmException;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.UUID;
//
///**
// * 顺丰快递 Api 接口对接， 官网文档-零担下单： https://freight.sf-express.com/api/api.html#id=30
// * <p>
// * 请求头必须添加： "Content-type","application/x-www-form-urlencoded;charset=UTF-8”
// * </P>
// *
// * <AUTHOR>
// * @version 1.0.0
// * @mail <EMAIL>
// * @date 2020/9/9 0009 9:38
// */
//
//@Component
//@Slf4j
//public class SFUtil {
//
//    @Autowired
//    private RestTemplateUtil restTemplateUtil;
//
//
//
//
//
//    /**
//     * 顺丰寄件下单
//     */
//    public ResponseCancel fopReceLtlCreateOrder(MsgData md) {
//        String msgDatas = JSON.toJSONString(md);
//        SFReturnData sfReturnData = http("EXP_RECE_CREATE_ORDER",msgDatas,SFConstants.SF_URL);
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ResponseCancel responseCreate = JSON.parseObject(apiResultData,ResponseCancel.class);
//        return responseCreate;
//    }
//
//
//    /**
//     * 顺丰取消订单
//     */
//    public ResponseCancel fopReceLtlCancelOrder(MsgDataUpdate msgDataUpdate) {
//        String md = JSON.toJSONString(msgDataUpdate);
//        SFReturnData sfReturnData = http("EXP_RECE_UPDATE_ORDER", md,SFConstants.SF_URL_CANCE);
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ResponseCancel responseCreate = JSON.parseObject(apiResultData,ResponseCancel.class);
//        return responseCreate;
//    }
//
//
//    /**
//     * 顺丰拦截取消订单
//     */
//    public ResponseCancel fopInterceptOrder(MsgDataIntercept msgDataIntercept) {
//        String md = JSON.toJSONString(msgDataIntercept);
//        SFReturnData sfReturnData = http("EXP_RECE_WANTED_INTERCEPT", md,SFConstants.SF_URL_Intercep);
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ResponseCancel responseCreate = JSON.parseObject(apiResultData,ResponseCancel.class);
//        return responseCreate;
//    }
//
//    /**
//     * 下单结果查询
//     */
//    public ResponseCancel fopReceLtlGetOrderResult(String orderId) {
//        //
//        Map<String, String> param = new HashMap<>();
//        param.put("orderId", orderId);
//        param.put("searchType","1");
//        param.put("language","zh-cn");
//        String msgDatas = JSON.toJSONString(param);
//        SFReturnData sfReturnData = http("EXP_RECE_SEARCH_ORDER_RESP",msgDatas,SFConstants.SF_URL_RESULT);
//
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ResponseCancel responseCreate = JSON.parseObject(apiResultData,ResponseCancel.class);
//        return responseCreate;
//    }
//
//    /**
//     * 订单路由查询
//     *
//     */
//    public ResponseCancel fopReceLtlGetOrderRouting(String masterWaybillNo) {
//        //
//        Map<String, String> param = new HashMap<>();
//        param.put("trackingType", "1");
//        param.put("trackingNumber", masterWaybillNo);
//        String msgDatas = JSON.toJSONString(param);
//        SFReturnData sfReturnData = http("EXP_RECE_SEARCH_ROUTES",msgDatas,SFConstants.SF_URL_Routing);
//
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ResponseCancel responseCreate = JSON.parseObject(apiResultData,ResponseCancel.class);
//        return responseCreate;
//    }
//
//    /**
//     * 顺丰电子面单
//     * @param msgDataExpress
//     * @return
//     */
//    public ExpressResponse getExpress(MsgDataExpress msgDataExpress) {
//        String md = JSON.toJSONString(msgDataExpress);
//        SFReturnData sfReturnData = http("COM_RECE_CLOUD_PRINT_WAYBILLS", md, SFConstants.SF_URL_Express);
//        if (!"A1000".equals(sfReturnData.getApiResultCode())) {
//            //失败
//            log.info(sfReturnData.toString());
//            throw new VcaException(sfReturnData.getApiErrorMsg());
//        }
//        String apiResultData = sfReturnData.getApiResultData();
//        ExpressResponse responseCreate = JSON.parseObject(apiResultData, ExpressResponse.class);
//        return responseCreate;
//    }
//
//
//    private SFReturnData http(String serviceCode,String md,String url) {
//        String timestamp = System.currentTimeMillis() + "";
//        // 发送快递参数处理
//        MultiValueMap<String, String> sendBody= new LinkedMultiValueMap<String, String>();
//        sendBody.add("partnerID", SFConstants.CLIENT_CODE);
//        sendBody.add("requestID", UUID.randomUUID().toString());
//        sendBody.add("serviceCode", serviceCode);
//        sendBody.add("timestamp", timestamp);
//        sendBody.add("msgDigest", genDigest(timestamp, md, SFConstants.CHECK_WORD));
//        sendBody.add("msgData", md);
//        //发送请求
//        String s = restTemplateUtil.postFormData(url, sendBody);
//        if (StringUtils.isEmpty(s)){
//            throw new VcaException("顺丰接口请求失败 无返回数据");
//        }
//        JSONObject sfJson = JSONObject.parseObject(s);
//        Map<String, Object> innerMap = sfJson.getInnerMap();
//        SFReturnData sfReturnData = new SFReturnData();
//        //如果apiResultData为空说明第三方请求错误 并返回错误信息
//        if (StringUtils.isEmpty(innerMap.get("apiResultData"))){
//            throw new VcaException(innerMap.get("apiErrorMsg").toString());
//        }
//        sfReturnData.setApiResponseID(innerMap.get("apiResponseID").toString());
//        sfReturnData.setApiResultData(innerMap.get("apiResultData").toString());
//        sfReturnData.setApiErrorMsg(innerMap.get("apiErrorMsg").toString());
//        sfReturnData.setApiResultCode(innerMap.get("apiResultCode").toString());
//        return sfReturnData;
//    }
//
//
//    /**
//     * 业务数据加密
//     *
//     * @param timestamp
//     * @param msgData
//     * @param md5key
//     * @return
//     * @throws Exception
//     */
//    private String genDigest(String timestamp, String msgData, String md5key) {
//        //将业务报文+时间戳+校验码组合成需加密的字符串(注意顺序)
//        String toVerifyText = msgData + timestamp + md5key;
//        //因业务报文中可能包含加号、空格等特殊字符，需要urlEnCode处理
//        try {
//            toVerifyText = URLEncoder.encode(toVerifyText, "UTF-8");
//        //进行Md5加密
//        MessageDigest md5 = MessageDigest.getInstance("MD5");
//        md5.update(toVerifyText.getBytes("UTF-8"));
//        byte[] md = md5.digest();
//        //通过BASE64生成数字签名
//        String msgDigest = new String(new BASE64Encoder().encode(md));
//        return msgDigest;
//        } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//
//}
//
