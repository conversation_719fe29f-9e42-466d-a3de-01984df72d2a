package com.vca.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 展览Response
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ExhibitionAdminResponse对象", description="展览Response")
public class ExhibitionAdminResponse implements Serializable {

    private static final long serialVersionUID = 6038844946101792200L;

    @ApiModelProperty(value = "展览主键id")
    private Long id;

    @ApiModelProperty(value = "展览名称")
    private String name;

    @ApiModelProperty(value = "英文展览名称")
    private String nameEn;

    @ApiModelProperty(value = "展览开始日期")
    private Date startTime;

    @ApiModelProperty(value = "展览结束日期")
    private Date endTime;

    @ApiModelProperty("展览日期")
    private List<String> time;

    @ApiModelProperty(value = "是否付费  0=免费  1=付费")
    private Integer isPay;

    @ApiModelProperty(value = "展览价格")
    private BigDecimal price;

    @ApiModelProperty(value = "展览开始时间（小时）")
    private String startHours;

    @ApiModelProperty(value = "展览结束时间（小时）")
    private String endHours;

    @ApiModelProperty(value = "展览地址id")
    private Integer courseAddressId;

    @ApiModelProperty(value = "展览地址")
    private String courseAddress;

    @ApiModelProperty(value = "展览城市名称")
    private String cityName;

    @ApiModelProperty(value = "英文展览城市名称")
    private String cityNameEn;

    @ApiModelProperty(value = "展览简介")
    private String introduction;

    @ApiModelProperty(value = "英文展览简介")
    private String introductionEn;

    @ApiModelProperty(value = "展览封面图")
    private String cover;

    @ApiModelProperty(value = "展览封面图 统一字段")
    private String coverImage;

    @ApiModelProperty(value = "展览轮播图")
    private String carouselMap;

    @ApiModelProperty(value = "是否可预定")
    private Integer isAppointment;

    @ApiModelProperty(value = "展览时间（小时）")
    private List<String> hours;

    @ApiModelProperty(value = "展览时长（小时）")
    private Float exhibitionHoursCount;

    @ApiModelProperty(value = "主图视频")
    private String mainPictureVideo;

    @ApiModelProperty(value = "分享海报")
    private String sharePoster;

    @ApiModelProperty(value = "预约状态")
    private Integer appointmentStatus;

    @ApiModelProperty(value = "限量")
    private Integer quota;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "官网链接")
    private String link;

    @ApiModelProperty(value = "展览详情")
    private String details;

    @ApiModelProperty(value = "英文展览详情")
    private String detailsEn;

    @ApiModelProperty(value = "相关课程")
    private List<CourseAboutAdminResponse> courseAboutAdminResponses;

    @ApiModelProperty(value = "视频号ID")
    private String channelsId;

    @ApiModelProperty(value = "视频id")
    private String videoId;
}
