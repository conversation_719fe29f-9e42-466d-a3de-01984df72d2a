package com.vca.common.response;

import com.vca.common.vo.WxPayJsResultVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:支付预下单响应参数
 * @author:chenbing
 * @date 2022/12/2 11:32
 */
@Data
@ApiModel(value = "支付预下单响应参数")
public class PaymentPreOrderResponse {

    @ApiModelProperty(value = "微信调起支付参数对象")
    private WxPayJsResultVo jsConfig;

    @ApiModelProperty(value = "支付类型")
    private String payType;

    @ApiModelProperty(value = "订单编号")
    private List<String> orderNos;

    @ApiModelProperty(value = "是否需要拉起支付 true=需要 false=不需要")
    private Boolean paymentRequire;
}
