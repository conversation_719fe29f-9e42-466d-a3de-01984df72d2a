package com.vca.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 讲师列表请求对象
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@ApiModel(value="讲师列表请求对象", description="讲师列表请求对象")
public class InstructorListRequest extends PageParamRequest{

    @ApiModelProperty(value = "讲师姓名")
    private String name;

    @ApiModelProperty(value = "课程ID列表")
    private List<Long> courseIds;
}
