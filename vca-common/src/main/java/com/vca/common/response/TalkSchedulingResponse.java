package com.vca.common.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2022/11/13   19:53
 */
@Data
public class TalkSchedulingResponse {
    @ApiModelProperty(value = "讲座排期主键id")
    private Long id;

    @ApiModelProperty(value = "讲座id")
    private Long talkId;

    @ApiModelProperty(value = "排期日期")
    private String schedulingDate;

    @ApiModelProperty(value = "排期当天开始时间")
    private String schedulingStartTime;

    @ApiModelProperty(value = "排期当天结束时间")
    private String schedulingEndTime;

    @ApiModelProperty(value = "语言")
    @TableField(exist = false)
    private String language;

    @ApiModelProperty(value = "讲座价格")
    private BigDecimal price;

    @ApiModelProperty(value = "限量")
    private Integer quota;

    @ApiModelProperty(value = "当前讲座所剩席位")
    @TableField(exist = false)
    private Integer schedulingSeatCount;

    @ApiModelProperty(value = "讲座状态 0=已结束 1=进行中 2=未开始")
    private Integer schedulingStatus;
}
